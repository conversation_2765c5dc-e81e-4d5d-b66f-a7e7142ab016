{"name": "@emotion/serialize", "version": "1.3.3", "description": "serialization utils for emotion", "main": "dist/emotion-serialize.cjs.js", "module": "dist/emotion-serialize.esm.js", "types": "dist/emotion-serialize.cjs.d.ts", "license": "MIT", "repository": "https://github.com/emotion-js/emotion/tree/main/packages/serialize", "publishConfig": {"access": "public"}, "scripts": {"test:typescript": "dtslint types"}, "dependencies": {"@emotion/hash": "^0.9.2", "@emotion/memoize": "^0.9.0", "@emotion/unitless": "^0.10.0", "@emotion/utils": "^1.4.2", "csstype": "^3.0.2"}, "devDependencies": {"@definitelytyped/dtslint": "0.0.112", "typescript": "^5.4.5"}, "files": ["src", "dist"], "exports": {".": {"types": {"import": "./dist/emotion-serialize.cjs.mjs", "default": "./dist/emotion-serialize.cjs.js"}, "development": {"module": "./dist/emotion-serialize.development.esm.js", "import": "./dist/emotion-serialize.development.cjs.mjs", "default": "./dist/emotion-serialize.development.cjs.js"}, "module": "./dist/emotion-serialize.esm.js", "import": "./dist/emotion-serialize.cjs.mjs", "default": "./dist/emotion-serialize.cjs.js"}, "./package.json": "./package.json"}, "imports": {"#is-development": {"development": "./src/conditions/true.ts", "default": "./src/conditions/false.ts"}}}