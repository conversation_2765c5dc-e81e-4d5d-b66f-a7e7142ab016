"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/repo-tools
Object.defineProperty(exports, "__esModule", { value: true });
exports.esnext_full = void 0;
const dom_1 = require("./dom");
const dom_iterable_1 = require("./dom.iterable");
const esnext_1 = require("./esnext");
const scripthost_1 = require("./scripthost");
const webworker_importscripts_1 = require("./webworker.importscripts");
exports.esnext_full = {
    ...esnext_1.esnext,
    ...dom_1.dom,
    ...webworker_importscripts_1.webworker_importscripts,
    ...scripthost_1.scripthost,
    ...dom_iterable_1.dom_iterable,
};
//# sourceMappingURL=esnext.full.js.map