"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SwitchScope = void 0;
const ScopeBase_1 = require("./ScopeBase");
const ScopeType_1 = require("./ScopeType");
class SwitchScope extends ScopeBase_1.ScopeBase {
    constructor(scopeManager, upperScope, block) {
        super(scope<PERSON><PERSON>ger, ScopeType_1.ScopeType.switch, upperScope, block, false);
    }
}
exports.SwitchScope = SwitchScope;
//# sourceMappingURL=SwitchScope.js.map