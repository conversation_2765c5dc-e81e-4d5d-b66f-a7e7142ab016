{"version": 3, "file": "ts-nodes.d.ts", "sourceRoot": "", "sources": ["../../src/ts-estree/ts-nodes.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,EAAE,MAAM,YAAY,CAAC;AAKtC,OAAO,QAAQ,YAAY,CAAC;IAE1B,UAAiB,YAAa,SAAQ,EAAE,CAAC,gBAAgB;KAAG;IAC5D,UAAiB,WAAY,SAAQ,EAAE,CAAC,eAAe;KAAG;IAE1D,UAAiB,mBAAoB,SAAQ,EAAE,CAAC,IAAI;KAAG;IAEvD,UAAiB,iBAAkB,SAAQ,EAAE,CAAC,IAAI;KAAG;IAErD,UAAiB,eAAgB,SAAQ,EAAE,CAAC,IAAI;KAAG;IACnD,UAAiB,gBAAiB,SAAQ,EAAE,CAAC,IAAI;KAAG;CACrD;AAGD,MAAM,MAAM,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;AAE9C,MAAM,MAAM,MAAM,GACd,EAAE,CAAC,QAAQ,GACX,EAAE,CAAC,UAAU,GACb,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,gBAAgB,GAEnB,EAAE,CAAC,YAAY,GAEf,EAAE,CAAC,WAAW,GACd,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,oBAAoB,GACvB,EAAE,CAAC,SAAS,GACZ,EAAE,CAAC,wBAAwB,GAE3B,EAAE,CAAC,wBAAwB,GAC3B,EAAE,CAAC,6BAA6B,GAChC,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,uBAAuB,GAC1B,EAAE,CAAC,oBAAoB,GACvB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,kBAAkB,GACrB,EAAE,CAAC,2BAA2B,GAC9B,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,oBAAoB,GACvB,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,sBAAsB,GACzB,EAAE,CAAC,qBAAqB,GACxB,EAAE,CAAC,sBAAsB,GACzB,EAAE,CAAC,sBAAsB,GACzB,EAAE,CAAC,yBAAyB,GAC5B,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,2BAA2B,GAE9B,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,oBAAoB,GACvB,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,qBAAqB,GACxB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,qBAAqB,GACxB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,0BAA0B,GAC7B,EAAE,CAAC,qBAAqB,GACxB,EAAE,CAAC,sBAAsB,GACzB,EAAE,CAAC,WAAW,GACd,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,qBAAqB,GACxB,EAAE,CAAC,kBAAkB,GACrB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,wBAAwB,GAC3B,EAAE,CAAC,6BAA6B,GAChC,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,kBAAkB,GACrB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,uBAAuB,GAC1B,EAAE,CAAC,sBAAsB,GACzB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,uBAAuB,GAC1B,EAAE,CAAC,wBAAwB,GAC3B,EAAE,CAAC,uBAAuB,GAC1B,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,2BAA2B,GAC9B,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,wBAAwB,GAC3B,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,UAAU,GACb,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,qBAAqB,GACxB,EAAE,CAAC,WAAW,GACd,EAAE,CAAC,kBAAkB,GACrB,EAAE,CAAC,kBAAkB,GACrB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,kBAAkB,GACrB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,OAAO,GACV,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,kBAAkB,GACrB,EAAE,CAAC,KAAK,GACR,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,WAAW,GACd,EAAE,CAAC,WAAW,GACd,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,SAAS,GACZ,EAAE,CAAC,UAAU,GACb,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,WAAW,GAEd,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,oBAAoB,GACvB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,oBAAoB,GACvB,EAAE,CAAC,UAAU,GACb,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,WAAW,GACd,EAAE,CAAC,uBAAuB,GAC1B,EAAE,CAAC,uBAAuB,GAC1B,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,0BAA0B,GAC7B,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,UAAU,GACb,EAAE,CAAC,MAAM,GAET,EAAE,CAAC,UAAU,GAEb,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,uBAAuB,GAC1B,EAAE,CAAC,uBAAuB,GAC1B,EAAE,CAAC,mBAAmB,GAGtB,EAAE,CAAC,KAAK,GACR,EAAE,CAAC,mBAAmB,GACtB,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,aAAa,GAChB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,eAAe,GAClB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,cAAc,GACjB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,YAAY,GACf,EAAE,CAAC,gBAAgB,GACnB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,oBAAoB,GACvB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,iBAAiB,GACpB,EAAE,CAAC,cAAc,CAAC"}