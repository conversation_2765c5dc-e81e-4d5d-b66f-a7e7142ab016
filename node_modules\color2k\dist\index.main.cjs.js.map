{"version": 3, "file": "index.main.cjs.js", "sources": ["../src/guard.ts", "../src/ColorError.ts", "../src/parseToRgba.ts", "../src/parseToHsla.ts", "../src/hsla.ts", "../src/adjustHue.ts", "../src/darken.ts", "../src/desaturate.ts", "../src/getLuminance.ts", "../src/getContrast.ts", "../src/rgba.ts", "../src/mix.ts", "../src/getScale.ts", "../src/hasBadContrast.ts", "../src/lighten.ts", "../src/transparentize.ts", "../src/opacify.ts", "../src/readableColorIsBlack.ts", "../src/readableColor.ts", "../src/saturate.ts", "../src/toHex.ts", "../src/toRgba.ts", "../src/toHsla.ts"], "sourcesContent": ["/**\n * A simple guard function:\n *\n * ```js\n * Math.min(Math.max(low, value), high)\n * ```\n */\nfunction guard(low: number, high: number, value: number): number {\n  return Math.min(Math.max(low, value), high);\n}\n\nexport default guard;\n", "class ColorError extends Error {\n  constructor(color: string) {\n    super(`Failed to parse color: \"${color}\"`);\n  }\n}\n\nexport default ColorError;\n", "import guard from './guard';\nimport ColorError from './ColorError';\n\n/**\n * Parses a color into red, gree, blue, alpha parts\n *\n * @param color the input color. Can be a RGB, RBGA, HSL, HSLA, or named color\n */\nfunction parseToRgba(color: string): [number, number, number, number] {\n  if (typeof color !== 'string') throw new ColorError(color);\n  if (color.trim().toLowerCase() === 'transparent') return [0, 0, 0, 0];\n\n  let normalizedColor = color.trim();\n  normalizedColor = namedColorRegex.test(color) ? nameToHex(color) : color;\n\n  const reducedHexMatch = reducedHexRegex.exec(normalizedColor);\n  if (reducedHexMatch) {\n    const arr = Array.from(reducedHexMatch).slice(1);\n    return [\n      ...arr.slice(0, 3).map((x) => parseInt(r(x, 2), 16)),\n      parseInt(r(arr[3] || 'f', 2), 16) / 255,\n    ] as [number, number, number, number];\n  }\n\n  const hexMatch = hexRegex.exec(normalizedColor);\n  if (hexMatch) {\n    const arr = Array.from(hexMatch).slice(1);\n    return [\n      ...arr.slice(0, 3).map((x) => parseInt(x, 16)),\n      parseInt(arr[3] || 'ff', 16) / 255,\n    ] as [number, number, number, number];\n  }\n\n  const rgbaMatch = rgbaRegex.exec(normalizedColor);\n  if (rgbaMatch) {\n    const arr = Array.from(rgbaMatch).slice(1);\n    return [\n      ...arr.slice(0, 3).map((x) => parseInt(x, 10)),\n      parseFloat(arr[3] || '1'),\n    ] as [number, number, number, number];\n  }\n\n  const hslaMatch = hslaRegex.exec(normalizedColor);\n  if (hslaMatch) {\n    const [h, s, l, a] = Array.from(hslaMatch).slice(1).map(parseFloat);\n    if (guard(0, 100, s) !== s) throw new ColorError(color);\n    if (guard(0, 100, l) !== l) throw new ColorError(color);\n    return [...hslToRgb(h, s, l), Number.isNaN(a) ? 1 : a] as [\n      number,\n      number,\n      number,\n      number\n    ];\n  }\n\n  throw new ColorError(color);\n}\n\nfunction hash(str: string) {\n  let hash = 5381;\n  let i = str.length;\n\n  while (i) {\n    hash = (hash * 33) ^ str.charCodeAt(--i);\n  }\n\n  /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */\n  return (hash >>> 0) % 2341;\n}\n\nconst colorToInt = (x: string) => parseInt(x.replace(/_/g, ''), 36);\n\nconst compressedColorMap =\n  '1q29ehhb 1n09sgk7 1kl1ekf_ _yl4zsno 16z9eiv3 1p29lhp8 _bd9zg04 17u0____ _iw9zhe5 _to73___ _r45e31e _7l6g016 _jh8ouiv _zn3qba8 1jy4zshs 11u87k0u 1ro9yvyo 1aj3xael 1gz9zjz0 _3w8l4xo 1bf1ekf_ _ke3v___ _4rrkb__ 13j776yz _646mbhl _nrjr4__ _le6mbhl 1n37ehkb _m75f91n _qj3bzfz 1939yygw 11i5z6x8 _1k5f8xs 1509441m 15t5lwgf _ae2th1n _tg1ugcv 1lp1ugcv 16e14up_ _h55rw7n _ny9yavn _7a11xb_ 1ih442g9 _pv442g9 1mv16xof 14e6y7tu 1oo9zkds 17d1cisi _4v9y70f _y98m8kc 1019pq0v 12o9zda8 _348j4f4 1et50i2o _8epa8__ _ts6senj 1o350i2o 1mi9eiuo 1259yrp0 1ln80gnw _632xcoy 1cn9zldc _f29edu4 1n490c8q _9f9ziet 1b94vk74 _m49zkct 1kz6s73a 1eu9dtog _q58s1rz 1dy9sjiq __u89jo3 _aj5nkwg _ld89jo3 13h9z6wx _qa9z2ii _l119xgq _bs5arju 1hj4nwk9 1qt4nwk9 1ge6wau6 14j9zlcw 11p1edc_ _ms1zcxe _439shk6 _jt9y70f _754zsow 1la40eju _oq5p___ _x279qkz 1fa5r3rv _yd2d9ip _424tcku _8y1di2_ _zi2uabw _yy7rn9h 12yz980_ __39ljp6 1b59zg0x _n39zfzp 1fy9zest _b33k___ _hp9wq92 1il50hz4 _io472ub _lj9z3eo 19z9ykg0 _8t8iu3a 12b9bl4a 1ak5yw0o _896v4ku _tb8k8lv _s59zi6t _c09ze0p 1lg80oqn 1id9z8wb _238nba5 1kq6wgdi _154zssg _tn3zk49 _da9y6tc 1sg7cv4f _r12jvtt 1gq5fmkz 1cs9rvci _lp9jn1c _xw1tdnb 13f9zje6 16f6973h _vo7ir40 _bt5arjf _rc45e4t _hr4e100 10v4e100 _hc9zke2 _w91egv_ _sj2r1kk 13c87yx8 _vqpds__ _ni8ggk8 _tj9yqfb 1ia2j4r4 _7x9b10u 1fc9ld4j 1eq9zldr _5j9lhpx _ez9zl6o _md61fzm'\n    .split(' ')\n    .reduce((acc, next) => {\n      const key = colorToInt(next.substring(0, 3));\n      const hex = colorToInt(next.substring(3)).toString(16);\n\n      // NOTE: padStart could be used here but it breaks Node 6 compat\n      // https://github.com/ricokahler/color2k/issues/351\n      let prefix = '';\n      for (let i = 0; i < 6 - hex.length; i++) {\n        prefix += '0';\n      }\n\n      acc[key] = `${prefix}${hex}`;\n      return acc;\n    }, {} as { [key: string]: string });\n\n/**\n * Checks if a string is a CSS named color and returns its equivalent hex value, otherwise returns the original color.\n */\nfunction nameToHex(color: string): string {\n  const normalizedColorName = color.toLowerCase().trim();\n  const result = compressedColorMap[hash(normalizedColorName)];\n  if (!result) throw new ColorError(color);\n  return `#${result}`;\n}\n\nconst r = (str: string, amount: number) =>\n  Array.from(Array(amount))\n    .map(() => str)\n    .join('');\n\nconst reducedHexRegex = new RegExp(`^#${r('([a-f0-9])', 3)}([a-f0-9])?$`, 'i');\nconst hexRegex = new RegExp(`^#${r('([a-f0-9]{2})', 3)}([a-f0-9]{2})?$`, 'i');\nconst rgbaRegex = new RegExp(\n  `^rgba?\\\\(\\\\s*(\\\\d+)\\\\s*${r(\n    ',\\\\s*(\\\\d+)\\\\s*',\n    2\n  )}(?:,\\\\s*([\\\\d.]+))?\\\\s*\\\\)$`,\n  'i'\n);\nconst hslaRegex =\n  /^hsla?\\(\\s*([\\d.]+)\\s*,\\s*([\\d.]+)%\\s*,\\s*([\\d.]+)%(?:\\s*,\\s*([\\d.]+))?\\s*\\)$/i;\nconst namedColorRegex = /^[a-z]+$/i;\n\nconst roundColor = (color: number): number => {\n  return Math.round(color * 255);\n};\n\nconst hslToRgb = (\n  hue: number,\n  saturation: number,\n  lightness: number\n): [number, number, number] => {\n  let l = lightness / 100;\n  if (saturation === 0) {\n    // achromatic\n    return [l, l, l].map(roundColor) as [number, number, number];\n  }\n\n  // formulae from https://en.wikipedia.org/wiki/HSL_and_HSV\n  const huePrime = (((hue % 360) + 360) % 360) / 60;\n  const chroma = (1 - Math.abs(2 * l - 1)) * (saturation / 100);\n  const secondComponent = chroma * (1 - Math.abs((huePrime % 2) - 1));\n\n  let red = 0;\n  let green = 0;\n  let blue = 0;\n\n  if (huePrime >= 0 && huePrime < 1) {\n    red = chroma;\n    green = secondComponent;\n  } else if (huePrime >= 1 && huePrime < 2) {\n    red = secondComponent;\n    green = chroma;\n  } else if (huePrime >= 2 && huePrime < 3) {\n    green = chroma;\n    blue = secondComponent;\n  } else if (huePrime >= 3 && huePrime < 4) {\n    green = secondComponent;\n    blue = chroma;\n  } else if (huePrime >= 4 && huePrime < 5) {\n    red = secondComponent;\n    blue = chroma;\n  } else if (huePrime >= 5 && huePrime < 6) {\n    red = chroma;\n    blue = secondComponent;\n  }\n\n  const lightnessModification = l - chroma / 2;\n  const finalRed = red + lightnessModification;\n  const finalGreen = green + lightnessModification;\n  const finalBlue = blue + lightnessModification;\n\n  return [finalRed, finalGreen, finalBlue].map(roundColor) as [\n    number,\n    number,\n    number\n  ];\n};\n\nexport default parseToRgba;\n", "// taken from:\n// https://github.com/styled-components/polished/blob/a23a6a2bb26802b3d922d9c3b67bac3f3a54a310/src/internalHelpers/_rgbToHsl.js\nimport parseToRgba from './parseToRgba';\n\n/**\n * Parses a color in hue, saturation, lightness, and the alpha channel.\n *\n * Hue is a number between 0 and 360, saturation, lightness, and alpha are\n * decimal percentages between 0 and 1\n */\nfunction parseToHsla(color: string): [number, number, number, number] {\n  const [red, green, blue, alpha] = parseToRgba(color).map((value, index) =>\n    // 3rd index is alpha channel which is already normalized\n    index === 3 ? value : value / 255\n  );\n\n  const max = Math.max(red, green, blue);\n  const min = Math.min(red, green, blue);\n  const lightness = (max + min) / 2;\n\n  // achromatic\n  if (max === min) return [0, 0, lightness, alpha];\n\n  const delta = max - min;\n  const saturation =\n    lightness > 0.5 ? delta / (2 - max - min) : delta / (max + min);\n\n  const hue =\n    60 *\n    (red === max\n      ? (green - blue) / delta + (green < blue ? 6 : 0)\n      : green === max\n      ? (blue - red) / delta + 2\n      : (red - green) / delta + 4);\n\n  return [hue, saturation, lightness, alpha];\n}\n\nexport default parseToHsla;\n", "import guard from './guard';\n\n/**\n * Takes in hsla parts and constructs an hsla string\n *\n * @param hue The color circle (from 0 to 360) - 0 (or 360) is red, 120 is green, 240 is blue\n * @param saturation Percentage of saturation, given as a decimal between 0 and 1\n * @param lightness Percentage of lightness, given as a decimal between 0 and 1\n * @param alpha Percentage of opacity, given as a decimal between 0 and 1\n */\nfunction hsla(\n  hue: number,\n  saturation: number,\n  lightness: number,\n  alpha: number\n): string {\n  return `hsla(${(hue % 360).toFixed()}, ${guard(\n    0,\n    100,\n    saturation * 100\n  ).toFixed()}%, ${guard(0, 100, lightness * 100).toFixed()}%, ${parseFloat(\n    guard(0, 1, alpha).toFixed(3)\n  )})`;\n}\n\nexport default hsla;\n", "import parseToHsla from './parseToHsla';\nimport hsla from './hsla';\n\n/**\n * Adjusts the current hue of the color by the given degrees. Wraps around when\n * over 360.\n *\n * @param color input color\n * @param degrees degrees to adjust the input color, accepts degree integers\n * (0 - 360) and wraps around on overflow\n */\nfunction adjustHue(color: string, degrees: number): string {\n  const [h, s, l, a] = parseToHsla(color);\n  return hsla(h + degrees, s, l, a);\n}\n\nexport default adjustHue;\n", "import parseToHsla from './parseToHsla';\nimport hsla from './hsla';\n\n/**\n * Darkens using lightness. This is equivalent to subtracting the lightness\n * from the L in HSL.\n *\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction darken(color: string, amount: number): string {\n  const [hue, saturation, lightness, alpha] = parseToHsla(color);\n  return hsla(hue, saturation, lightness - amount, alpha);\n}\n\nexport default darken;\n", "import parseToHsla from './parseToHsla';\nimport hsla from './hsla';\n\n/**\n * Desaturates the input color by the given amount via subtracting from the `s`\n * in `hsla`.\n *\n * @param amount The amount to desaturate, given as a decimal between 0 and 1\n */\nfunction desaturate(color: string, amount: number): string {\n  const [h, s, l, a] = parseToHsla(color);\n  return hsla(h, s - amount, l, a);\n}\n\nexport default desaturate;\n", "import parseToRgba from './parseToRgba';\n// taken from:\n// https://github.com/styled-components/polished/blob/0764c982551b487469043acb56281b0358b3107b/src/color/getLuminance.js\n\n/**\n * Returns a number (float) representing the luminance of a color.\n */\nfunction getLuminance(color: string): number {\n  if (color === 'transparent') return 0;\n\n  function f(x: number) {\n    const channel = x / 255;\n    return channel <= 0.04045\n      ? channel / 12.92\n      : Math.pow(((channel + 0.055) / 1.055), 2.4);\n  }\n\n  const [r, g, b] = parseToRgba(color);\n  return 0.2126 * f(r) + 0.7152 * f(g) + 0.0722 * f(b);\n}\n\nexport default getLuminance;\n", "// taken from:\n// https://github.com/styled-components/polished/blob/0764c982551b487469043acb56281b0358b3107b/src/color/getContrast.js\nimport getLuminance from './getLuminance';\n\n/**\n * Returns the contrast ratio between two colors based on\n * [W3's recommended equation for calculating contrast](http://www.w3.org/TR/WCAG20/#contrast-ratiodef).\n */\nfunction getContrast(color1: string, color2: string): number {\n  const luminance1 = getLuminance(color1);\n  const luminance2 = getLuminance(color2);\n\n  return luminance1 > luminance2\n    ? (luminance1 + 0.05) / (luminance2 + 0.05)\n    : (luminance2 + 0.05) / (luminance1 + 0.05);\n}\n\nexport default getContrast;\n", "import guard from './guard';\n\n/**\n * Takes in rgba parts and returns an rgba string\n *\n * @param red The amount of red in the red channel, given in a number between 0 and 255 inclusive\n * @param green The amount of green in the red channel, given in a number between 0 and 255 inclusive\n * @param blue The amount of blue in the red channel, given in a number between 0 and 255 inclusive\n * @param alpha Percentage of opacity, given as a decimal between 0 and 1\n */\nfunction rgba(red: number, green: number, blue: number, alpha: number): string {\n  return `rgba(${guard(0, 255, red).toFixed()}, ${guard(\n    0,\n    255,\n    green\n  ).toFixed()}, ${guard(0, 255, blue).toFixed()}, ${parseFloat(\n    guard(0, 1, alpha).toFixed(3)\n  )})`;\n}\n\nexport default rgba;\n", "import parseToRgba from './parseToRgba';\nimport rgba from './rgba';\n\n/**\n * Mixes two colors together. Taken from sass's implementation.\n */\nfunction mix(color1: string, color2: string, weight: number): string {\n  const normalize = (n: number, index: number) =>\n    // 3rd index is alpha channel which is already normalized\n    index === 3 ? n : n / 255;\n\n  const [r1, g1, b1, a1] = parseToRgba(color1).map(normalize);\n  const [r2, g2, b2, a2] = parseToRgba(color2).map(normalize);\n\n  // The formula is copied from the original Sass implementation:\n  // http://sass-lang.com/documentation/Sass/Script/Functions.html#mix-instance_method\n  const alphaDelta = a2 - a1;\n  const normalizedWeight = weight * 2 - 1;\n  const combinedWeight =\n    normalizedWeight * alphaDelta === -1\n      ? normalizedWeight\n      : normalizedWeight + alphaDelta / (1 + normalizedWeight * alphaDelta);\n  const weight2 = (combinedWeight + 1) / 2;\n  const weight1 = 1 - weight2;\n\n  const r = (r1 * weight1 + r2 * weight2) * 255;\n  const g = (g1 * weight1 + g2 * weight2) * 255;\n  const b = (b1 * weight1 + b2 * weight2) * 255;\n  const a = a2 * weight + a1 * (1 - weight);\n\n  return rgba(r, g, b, a);\n}\n\nexport default mix;\n", "import mix from './mix';\nimport guard from './guard';\n\n/**\n * Given a series colors, this function will return a `scale(x)` function that\n * accepts a percentage as a decimal between 0 and 1 and returns the color at\n * that percentage in the scale.\n *\n * ```js\n * const scale = getScale('red', 'yellow', 'green');\n * console.log(scale(0)); // rgba(255, 0, 0, 1)\n * console.log(scale(0.5)); // rgba(255, 255, 0, 1)\n * console.log(scale(1)); // rgba(0, 128, 0, 1)\n * ```\n *\n * If you'd like to limit the domain and range like chroma-js, we recommend\n * wrapping scale again.\n *\n * ```js\n * const _scale = getScale('red', 'yellow', 'green');\n * const scale = x => _scale(x / 100);\n *\n * console.log(scale(0)); // rgba(255, 0, 0, 1)\n * console.log(scale(50)); // rgba(255, 255, 0, 1)\n * console.log(scale(100)); // rgba(0, 128, 0, 1)\n * ```\n */\nfunction getScale(...colors: string[]): (n: number) => string {\n  return (n) => {\n    const lastIndex = colors.length - 1;\n    const lowIndex = guard(0, lastIndex, Math.floor(n * lastIndex));\n    const highIndex = guard(0, lastIndex, Math.ceil(n * lastIndex));\n\n    const color1 = colors[lowIndex];\n    const color2 = colors[highIndex];\n\n    const unit = 1 / lastIndex;\n    const weight = (n - unit * lowIndex) / unit;\n\n    return mix(color1, color2, weight);\n  };\n}\n\nexport default getScale;\n", "import getContrast from './getContrast';\n\nconst guidelines = {\n  decorative: 1.5,\n  readable: 3,\n  aa: 4.5,\n  aaa: 7,\n};\n\n/**\n * Returns whether or not a color has bad contrast against a background\n * according to a given standard.\n */\nfunction hasBadContrast(\n  color: string,\n  standard: 'decorative' | 'readable' | 'aa' | 'aaa' = 'aa',\n  background: string = '#fff'\n): boolean {\n  return getContrast(color, background) < guidelines[standard];\n}\n\nexport default hasBadContrast;\n", "import darken from './darken';\n/**\n * Lightens a color by a given amount. This is equivalent to\n * `darken(color, -amount)`\n *\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction lighten(color: string, amount: number): string {\n  return darken(color, -amount);\n}\n\nexport default lighten;\n", "import parseToRgba from './parseToRgba';\nimport rgba from './rgba';\n\n/**\n * Takes in a color and makes it more transparent by convert to `rgba` and\n * decreasing the amount in the alpha channel.\n *\n * @param amount The amount to increase the transparency by, given as a decimal between 0 and 1\n */\nfunction transparentize(color: string, amount: number): string {\n  const [r, g, b, a] = parseToRgba(color);\n  return rgba(r, g, b, a - amount);\n}\n\nexport default transparentize;\n", "import transparentize from './transparentize';\n\n/**\n * Takes a color and un-transparentizes it. Equivalent to\n * `transparentize(color, -amount)`\n *\n * @param amount The amount to increase the opacity by, given as a decimal between 0 and 1\n */\nfunction opacify(color: string, amount: number): string {\n  return transparentize(color, -amount);\n}\n\nexport default opacify;\n", "import getLuminance from './getLuminance';\n\n/**\n * An alternative function to `readableColor`. Returns whether or not the \n * readable color (i.e. the color to be place on top the input color) should be\n * black.\n */\nfunction readableColorIsBlack(color: string): boolean {\n  return getLuminance(color) > 0.179;\n}\n\nexport default readableColorIsBlack;\n", "import readableColorIsBlack from './readableColorIsBlack';\n\n/**\n * Returns black or white for best contrast depending on the luminosity of the\n * given color.\n */\nfunction readableColor(color: string): string {\n  return readableColorIsBlack(color) ? '#000' : '#fff';\n}\n\nexport default readableColor;\n", "import desaturate from './desaturate';\n\n/**\n * Saturates a color by converting it to `hsl` and increasing the saturation\n * amount. Equivalent to `desaturate(color, -amount)`\n * \n * @param color Input color\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction saturate(color: string, amount: number): string {\n  return desaturate(color, -amount);\n}\n\nexport default saturate;\n", "import parseToRgba from './parseToRgba';\nimport guard from './guard';\n\n/**\n * Takes in any color and returns it as a hex code.\n */\nfunction toHex(color: string): string {\n  const [r, g, b, a] = parseToRgba(color);\n\n  let hex = (x: number) => {\n    const h = guard(0, 255, x).toString(16);\n    // NOTE: padStart could be used here but it breaks Node 6 compat\n    // https://github.com/ricokahler/color2k/issues/351\n    return h.length === 1 ? `0${h}` : h;\n  };\n\n  return `#${hex(r)}${hex(g)}${hex(b)}${a < 1 ? hex(Math.round(a * 255)) : ''}`;\n}\n\nexport default toHex;\n", "import parseToRgba from './parseToRgba';\nimport rgba from './rgba';\n\n/**\n * Takes in any color and returns it as an rgba string.\n */\nfunction toRgba(color: string): string {\n  return rgba(...parseToRgba(color));\n}\n\nexport default toRgba;\n", "import parseToHsla from './parseToHsla';\nimport hsla from './hsla';\n\n/**\n * Takes in any color and returns it as an hsla string.\n */\nfunction toHsla(color: string): string {\n  return hsla(...parseToHsla(color));\n}\n\nexport default toHsla;\n"], "names": ["guard", "low", "high", "value", "Math", "min", "max", "ColorError", "_Error", "_inherits", "_super", "_createSuper", "color", "_classCallCheck", "call", "_createClass", "_wrapNativeSuper", "Error", "parseToRgba", "trim", "toLowerCase", "normalizedColor", "namedColorRegex", "test", "nameToHex", "reducedHexMatch", "reducedHexRegex", "exec", "arr", "Array", "from", "slice", "concat", "_toConsumableArray", "map", "x", "parseInt", "r", "hexMatch", "hexRegex", "rgbaMatch", "rgbaRegex", "parseFloat", "hslaMatch", "hslaRegex", "_Array$from$slice$map", "_Array$from$slice$map2", "_slicedToArray", "h", "s", "l", "a", "hslToRgb", "Number", "isNaN", "hash", "str", "i", "length", "charCodeAt", "colorToInt", "replace", "compressedColorMap", "split", "reduce", "acc", "next", "key", "substring", "hex", "toString", "prefix", "normalizedColorName", "result", "amount", "join", "RegExp", "roundColor", "round", "hue", "saturation", "lightness", "huePrime", "chroma", "abs", "secondComponent", "red", "green", "blue", "lightnessModification", "finalRed", "finalGreen", "finalBlue", "parseToHsla", "_parseToRgba$map", "index", "_parseToRgba$map2", "alpha", "delta", "hsla", "toFixed", "adjustHue", "degrees", "_parseToHsla", "_parseToHsla2", "darken", "desaturate", "getLuminance", "f", "channel", "pow", "_parseToRgba", "_parseToRgba2", "g", "b", "getContrast", "color1", "color2", "luminance1", "luminance2", "rgba", "mix", "weight", "normalize", "n", "r1", "g1", "b1", "a1", "_parseToRgba$map3", "_parseToRgba$map4", "r2", "g2", "b2", "a2", "alphaDelta", "normalizedWeight", "combinedWeight", "weight2", "weight1", "getScale", "_len", "arguments", "colors", "_key", "lastIndex", "lowIndex", "floor", "highIndex", "ceil", "unit", "guidelines", "decorative", "readable", "aa", "aaa", "hasBadContrast", "standard", "undefined", "background", "lighten", "transparentize", "opacify", "readableColorIsBlack", "readableColor", "saturate", "toHex", "toRgba", "apply", "toHsla"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,KAAKA,CAACC,GAAW,EAAEC,IAAY,EAAEC,KAAa,EAAU;AAC/D,EAAA,OAAOC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACL,GAAG,EAAEE,KAAK,CAAC,EAAED,IAAI,CAAC,CAAA;AAC7C;;ACTMK,IAAAA,UAAU,0BAAAC,MAAA,EAAA;EAAAC,SAAA,CAAAF,UAAA,EAAAC,MAAA,CAAA,CAAA;AAAA,EAAA,IAAAE,MAAA,GAAAC,YAAA,CAAAJ,UAAA,CAAA,CAAA;EACd,SAAAA,UAAAA,CAAYK,KAAa,EAAE;AAAAC,IAAAA,eAAA,OAAAN,UAAA,CAAA,CAAA;AAAA,IAAA,OAAAG,MAAA,CAAAI,IAAA,CAClB,IAAA,EAAA,CAAA,wBAAA,EAA0BF,KAAM,CAAE,CAAA,CAAA,CAAA,CAAA;AAC3C,GAAA;EAAC,OAAAG,YAAA,CAAAR,UAAA,CAAA,CAAA;AAAA,CAAAS,eAAAA,gBAAA,CAHsBC,KAAK,CAAA,CAAA,CAAA;AAM9B,mBAAeV,UAAU;;ACHzB;AACA;AACA;AACA;AACA;AACA,SAASW,WAAWA,CAACN,KAAa,EAAoC;EACpE,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,MAAM,IAAIL,YAAU,CAACK,KAAK,CAAC,CAAA;EAC1D,IAAIA,KAAK,CAACO,IAAI,EAAE,CAACC,WAAW,EAAE,KAAK,aAAa,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;AAErE,EAAA,IAAIC,eAAe,GAAGT,KAAK,CAACO,IAAI,EAAE,CAAA;AAClCE,EAAAA,eAAe,GAAGC,eAAe,CAACC,IAAI,CAACX,KAAK,CAAC,GAAGY,SAAS,CAACZ,KAAK,CAAC,GAAGA,KAAK,CAAA;AAExE,EAAA,IAAMa,eAAe,GAAGC,eAAe,CAACC,IAAI,CAACN,eAAe,CAAC,CAAA;AAC7D,EAAA,IAAII,eAAe,EAAE;AACnB,IAAA,IAAMG,GAAG,GAAGC,KAAK,CAACC,IAAI,CAACL,eAAe,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,CAAA;AAChD,IAAA,OAAA,EAAA,CAAAC,MAAA,CAAAC,kBAAA,CACKL,GAAG,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACG,GAAG,CAAC,UAACC,CAAC,EAAA;MAAA,OAAKC,QAAQ,CAACC,CAAC,CAACF,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;AAAA,KAAA,CAAC,IACpDC,QAAQ,CAACC,CAAC,CAACT,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAA,CAAA,CAAA;AAE3C,GAAA;AAEA,EAAA,IAAMU,QAAQ,GAAGC,QAAQ,CAACZ,IAAI,CAACN,eAAe,CAAC,CAAA;AAC/C,EAAA,IAAIiB,QAAQ,EAAE;AACZ,IAAA,IAAMV,IAAG,GAAGC,KAAK,CAACC,IAAI,CAACQ,QAAQ,CAAC,CAACP,KAAK,CAAC,CAAC,CAAC,CAAA;AACzC,IAAA,OAAA,EAAA,CAAAC,MAAA,CAAAC,kBAAA,CACKL,IAAG,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACG,GAAG,CAAC,UAACC,CAAC,EAAA;AAAA,MAAA,OAAKC,QAAQ,CAACD,CAAC,EAAE,EAAE,CAAC,CAAA;AAAA,KAAA,CAAC,CAC9CC,EAAAA,CAAAA,QAAQ,CAACR,IAAG,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,CAAA,CAAA,CAAA;AAEtC,GAAA;AAEA,EAAA,IAAMY,SAAS,GAAGC,SAAS,CAACd,IAAI,CAACN,eAAe,CAAC,CAAA;AACjD,EAAA,IAAImB,SAAS,EAAE;AACb,IAAA,IAAMZ,KAAG,GAAGC,KAAK,CAACC,IAAI,CAACU,SAAS,CAAC,CAACT,KAAK,CAAC,CAAC,CAAC,CAAA;AAC1C,IAAA,OAAA,EAAA,CAAAC,MAAA,CAAAC,kBAAA,CACKL,KAAG,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACG,GAAG,CAAC,UAACC,CAAC,EAAA;AAAA,MAAA,OAAKC,QAAQ,CAACD,CAAC,EAAE,EAAE,CAAC,CAAA;KAAC,CAAA,CAAA,EAAA,CAC9CO,UAAU,CAACd,KAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAA,CAAA,CAAA;AAE7B,GAAA;AAEA,EAAA,IAAMe,SAAS,GAAGC,SAAS,CAACjB,IAAI,CAACN,eAAe,CAAC,CAAA;AACjD,EAAA,IAAIsB,SAAS,EAAE;AACb,IAAA,IAAAE,qBAAA,GAAqBhB,KAAK,CAACC,IAAI,CAACa,SAAS,CAAC,CAACZ,KAAK,CAAC,CAAC,CAAC,CAACG,GAAG,CAACQ,UAAU,CAAC;MAAAI,sBAAA,GAAAC,cAAA,CAAAF,qBAAA,EAAA,CAAA,CAAA;AAA5DG,MAAAA,CAAC,GAAAF,sBAAA,CAAA,CAAA,CAAA;AAAEG,MAAAA,CAAC,GAAAH,sBAAA,CAAA,CAAA,CAAA;AAAEI,MAAAA,CAAC,GAAAJ,sBAAA,CAAA,CAAA,CAAA;AAAEK,MAAAA,CAAC,GAAAL,sBAAA,CAAA,CAAA,CAAA,CAAA;AACjB,IAAA,IAAI9C,KAAK,CAAC,CAAC,EAAE,GAAG,EAAEiD,CAAC,CAAC,KAAKA,CAAC,EAAE,MAAM,IAAI1C,YAAU,CAACK,KAAK,CAAC,CAAA;AACvD,IAAA,IAAIZ,KAAK,CAAC,CAAC,EAAE,GAAG,EAAEkD,CAAC,CAAC,KAAKA,CAAC,EAAE,MAAM,IAAI3C,YAAU,CAACK,KAAK,CAAC,CAAA;IACvD,OAAAoB,EAAAA,CAAAA,MAAA,CAAAC,kBAAA,CAAWmB,QAAQ,CAACJ,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,CAAEG,EAAAA,CAAAA,MAAM,CAACC,KAAK,CAACH,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAA,CAAA,CAAA;AAMvD,GAAA;AAEA,EAAA,MAAM,IAAI5C,YAAU,CAACK,KAAK,CAAC,CAAA;AAC7B,CAAA;AAEA,SAAS2C,IAAIA,CAACC,GAAW,EAAE;EACzB,IAAID,IAAI,GAAG,IAAI,CAAA;AACf,EAAA,IAAIE,CAAC,GAAGD,GAAG,CAACE,MAAM,CAAA;AAElB,EAAA,OAAOD,CAAC,EAAE;IACRF,IAAI,GAAIA,IAAI,GAAG,EAAE,GAAIC,GAAG,CAACG,UAAU,CAAC,EAAEF,CAAC,CAAC,CAAA;AAC1C,GAAA;;AAEA;AACF;AACA;AACE,EAAA,OAAO,CAACF,IAAI,KAAK,CAAC,IAAI,IAAI,CAAA;AAC5B,CAAA;AAEA,IAAMK,UAAU,GAAG,SAAbA,UAAUA,CAAIzB,CAAS,EAAA;AAAA,EAAA,OAAKC,QAAQ,CAACD,CAAC,CAAC0B,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAA;AAAA,CAAA,CAAA;AAEnE,IAAMC,kBAAkB,GACtB,qzCAAqzC,CAClzCC,KAAK,CAAC,GAAG,CAAC,CACVC,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI,EAAK;AACrB,EAAA,IAAMC,GAAG,GAAGP,UAAU,CAACM,IAAI,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;AAC5C,EAAA,IAAMC,GAAG,GAAGT,UAAU,CAACM,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC,CAAC,CAACE,QAAQ,CAAC,EAAE,CAAC,CAAA;;AAEtD;AACA;EACA,IAAIC,MAAM,GAAG,EAAE,CAAA;AACf,EAAA,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,GAAGY,GAAG,CAACX,MAAM,EAAED,CAAC,EAAE,EAAE;AACvCc,IAAAA,MAAM,IAAI,GAAG,CAAA;AACf,GAAA;EAEAN,GAAG,CAACE,GAAG,CAAC,GAAI,GAAEI,MAAO,CAAA,EAAEF,GAAI,CAAC,CAAA,CAAA;AAC5B,EAAA,OAAOJ,GAAG,CAAA;AACZ,CAAC,EAAE,EAA+B,CAAC,CAAA;;AAEvC;AACA;AACA;AACA,SAASzC,SAASA,CAACZ,KAAa,EAAU;EACxC,IAAM4D,mBAAmB,GAAG5D,KAAK,CAACQ,WAAW,EAAE,CAACD,IAAI,EAAE,CAAA;EACtD,IAAMsD,MAAM,GAAGX,kBAAkB,CAACP,IAAI,CAACiB,mBAAmB,CAAC,CAAC,CAAA;EAC5D,IAAI,CAACC,MAAM,EAAE,MAAM,IAAIlE,YAAU,CAACK,KAAK,CAAC,CAAA;EACxC,OAAQ,CAAA,CAAA,EAAG6D,MAAO,CAAC,CAAA,CAAA;AACrB,CAAA;AAEA,IAAMpC,CAAC,GAAG,SAAJA,CAACA,CAAImB,GAAW,EAAEkB,MAAc,EAAA;EAAA,OACpC7C,KAAK,CAACC,IAAI,CAACD,KAAK,CAAC6C,MAAM,CAAC,CAAC,CACtBxC,GAAG,CAAC,YAAA;AAAA,IAAA,OAAMsB,GAAG,CAAA;AAAA,GAAA,CAAC,CACdmB,IAAI,CAAC,EAAE,CAAC,CAAA;AAAA,CAAA,CAAA;AAEb,IAAMjD,eAAe,GAAG,IAAIkD,MAAM,CAAE,CAAIvC,EAAAA,EAAAA,CAAC,CAAC,YAAY,EAAE,CAAC,CAAE,CAAa,YAAA,CAAA,EAAE,GAAG,CAAC,CAAA;AAC9E,IAAME,QAAQ,GAAG,IAAIqC,MAAM,CAAE,CAAIvC,EAAAA,EAAAA,CAAC,CAAC,eAAe,EAAE,CAAC,CAAE,CAAgB,eAAA,CAAA,EAAE,GAAG,CAAC,CAAA;AAC7E,IAAMI,SAAS,GAAG,IAAImC,MAAM,CACzB,CAAyBvC,uBAAAA,EAAAA,CAAC,CACzB,iBAAiB,EACjB,CACF,CAAE,CAA4B,2BAAA,CAAA,EAC9B,GACF,CAAC,CAAA;AACD,IAAMO,SAAS,GACb,gFAAgF,CAAA;AAClF,IAAMtB,eAAe,GAAG,WAAW,CAAA;AAEnC,IAAMuD,UAAU,GAAG,SAAbA,UAAUA,CAAIjE,KAAa,EAAa;AAC5C,EAAA,OAAOR,IAAI,CAAC0E,KAAK,CAAClE,KAAK,GAAG,GAAG,CAAC,CAAA;AAChC,CAAC,CAAA;AAED,IAAMwC,QAAQ,GAAG,SAAXA,QAAQA,CACZ2B,GAAW,EACXC,UAAkB,EAClBC,SAAiB,EACY;AAC7B,EAAA,IAAI/B,CAAC,GAAG+B,SAAS,GAAG,GAAG,CAAA;EACvB,IAAID,UAAU,KAAK,CAAC,EAAE;AACpB;IACA,OAAO,CAAC9B,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,CAAChB,GAAG,CAAC2C,UAAU,CAAC,CAAA;AAClC,GAAA;;AAEA;EACA,IAAMK,QAAQ,GAAI,CAAEH,GAAG,GAAG,GAAG,GAAI,GAAG,IAAI,GAAG,GAAI,EAAE,CAAA;AACjD,EAAA,IAAMI,MAAM,GAAG,CAAC,CAAC,GAAG/E,IAAI,CAACgF,GAAG,CAAC,CAAC,GAAGlC,CAAC,GAAG,CAAC,CAAC,KAAK8B,UAAU,GAAG,GAAG,CAAC,CAAA;AAC7D,EAAA,IAAMK,eAAe,GAAGF,MAAM,IAAI,CAAC,GAAG/E,IAAI,CAACgF,GAAG,CAAEF,QAAQ,GAAG,CAAC,GAAI,CAAC,CAAC,CAAC,CAAA;EAEnE,IAAII,GAAG,GAAG,CAAC,CAAA;EACX,IAAIC,KAAK,GAAG,CAAC,CAAA;EACb,IAAIC,IAAI,GAAG,CAAC,CAAA;AAEZ,EAAA,IAAIN,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;AACjCI,IAAAA,GAAG,GAAGH,MAAM,CAAA;AACZI,IAAAA,KAAK,GAAGF,eAAe,CAAA;GACxB,MAAM,IAAIH,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;AACxCI,IAAAA,GAAG,GAAGD,eAAe,CAAA;AACrBE,IAAAA,KAAK,GAAGJ,MAAM,CAAA;GACf,MAAM,IAAID,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;AACxCK,IAAAA,KAAK,GAAGJ,MAAM,CAAA;AACdK,IAAAA,IAAI,GAAGH,eAAe,CAAA;GACvB,MAAM,IAAIH,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;AACxCK,IAAAA,KAAK,GAAGF,eAAe,CAAA;AACvBG,IAAAA,IAAI,GAAGL,MAAM,CAAA;GACd,MAAM,IAAID,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;AACxCI,IAAAA,GAAG,GAAGD,eAAe,CAAA;AACrBG,IAAAA,IAAI,GAAGL,MAAM,CAAA;GACd,MAAM,IAAID,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;AACxCI,IAAAA,GAAG,GAAGH,MAAM,CAAA;AACZK,IAAAA,IAAI,GAAGH,eAAe,CAAA;AACxB,GAAA;AAEA,EAAA,IAAMI,qBAAqB,GAAGvC,CAAC,GAAGiC,MAAM,GAAG,CAAC,CAAA;AAC5C,EAAA,IAAMO,QAAQ,GAAGJ,GAAG,GAAGG,qBAAqB,CAAA;AAC5C,EAAA,IAAME,UAAU,GAAGJ,KAAK,GAAGE,qBAAqB,CAAA;AAChD,EAAA,IAAMG,SAAS,GAAGJ,IAAI,GAAGC,qBAAqB,CAAA;EAE9C,OAAO,CAACC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,CAAC,CAAC1D,GAAG,CAAC2C,UAAU,CAAC,CAAA;AAK1D,CAAC;;AC1KD;AACA;AACA;AACA;AACA;AACA;AACA,SAASgB,WAAWA,CAACjF,KAAa,EAAoC;AACpE,EAAA,IAAAkF,gBAAA,GAAkC5E,WAAW,CAACN,KAAK,CAAC,CAACsB,GAAG,CAAC,UAAC/B,KAAK,EAAE4F,KAAK,EAAA;AAAA,MAAA;AACpE;AACAA,QAAAA,KAAK,KAAK,CAAC,GAAG5F,KAAK,GAAGA,KAAK,GAAG,GAAA;AAAG,QAAA;AAAA,KACnC,CAAC;IAAA6F,iBAAA,GAAAjD,cAAA,CAAA+C,gBAAA,EAAA,CAAA,CAAA;AAHMR,IAAAA,GAAG,GAAAU,iBAAA,CAAA,CAAA,CAAA;AAAET,IAAAA,KAAK,GAAAS,iBAAA,CAAA,CAAA,CAAA;AAAER,IAAAA,IAAI,GAAAQ,iBAAA,CAAA,CAAA,CAAA;AAAEC,IAAAA,KAAK,GAAAD,iBAAA,CAAA,CAAA,CAAA,CAAA;EAK9B,IAAM1F,GAAG,GAAGF,IAAI,CAACE,GAAG,CAACgF,GAAG,EAAEC,KAAK,EAAEC,IAAI,CAAC,CAAA;EACtC,IAAMnF,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACiF,GAAG,EAAEC,KAAK,EAAEC,IAAI,CAAC,CAAA;AACtC,EAAA,IAAMP,SAAS,GAAG,CAAC3E,GAAG,GAAGD,GAAG,IAAI,CAAC,CAAA;;AAEjC;AACA,EAAA,IAAIC,GAAG,KAAKD,GAAG,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE4E,SAAS,EAAEgB,KAAK,CAAC,CAAA;AAEhD,EAAA,IAAMC,KAAK,GAAG5F,GAAG,GAAGD,GAAG,CAAA;EACvB,IAAM2E,UAAU,GACdC,SAAS,GAAG,GAAG,GAAGiB,KAAK,IAAI,CAAC,GAAG5F,GAAG,GAAGD,GAAG,CAAC,GAAG6F,KAAK,IAAI5F,GAAG,GAAGD,GAAG,CAAC,CAAA;EAEjE,IAAM0E,GAAG,GACP,EAAE,IACDO,GAAG,KAAKhF,GAAG,GACR,CAACiF,KAAK,GAAGC,IAAI,IAAIU,KAAK,IAAIX,KAAK,GAAGC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,GAC/CD,KAAK,KAAKjF,GAAG,GACb,CAACkF,IAAI,GAAGF,GAAG,IAAIY,KAAK,GAAG,CAAC,GACxB,CAACZ,GAAG,GAAGC,KAAK,IAAIW,KAAK,GAAG,CAAC,CAAC,CAAA;EAEhC,OAAO,CAACnB,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAEgB,KAAK,CAAC,CAAA;AAC5C;;AClCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,IAAIA,CACXpB,GAAW,EACXC,UAAkB,EAClBC,SAAiB,EACjBgB,KAAa,EACL;EACR,OAAQ,CAAA,KAAA,EAAO,CAAClB,GAAG,GAAG,GAAG,EAAEqB,OAAO,EAAG,CAAIpG,EAAAA,EAAAA,KAAK,CAC5C,CAAC,EACD,GAAG,EACHgF,UAAU,GAAG,GACf,CAAC,CAACoB,OAAO,EAAG,CAAKpG,GAAAA,EAAAA,KAAK,CAAC,CAAC,EAAE,GAAG,EAAEiF,SAAS,GAAG,GAAG,CAAC,CAACmB,OAAO,EAAG,CAAK1D,GAAAA,EAAAA,UAAU,CACvE1C,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEiG,KAAK,CAAC,CAACG,OAAO,CAAC,CAAC,CAC9B,CAAE,CAAE,CAAA,CAAA,CAAA;AACN;;ACpBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACzF,KAAa,EAAE0F,OAAe,EAAU;AACzD,EAAA,IAAAC,YAAA,GAAqBV,WAAW,CAACjF,KAAK,CAAC;IAAA4F,aAAA,GAAAzD,cAAA,CAAAwD,YAAA,EAAA,CAAA,CAAA;AAAhCvD,IAAAA,CAAC,GAAAwD,aAAA,CAAA,CAAA,CAAA;AAAEvD,IAAAA,CAAC,GAAAuD,aAAA,CAAA,CAAA,CAAA;AAAEtD,IAAAA,CAAC,GAAAsD,aAAA,CAAA,CAAA,CAAA;AAAErD,IAAAA,CAAC,GAAAqD,aAAA,CAAA,CAAA,CAAA,CAAA;EACjB,OAAOL,IAAI,CAACnD,CAAC,GAAGsD,OAAO,EAAErD,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,CAAA;AACnC;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsD,MAAMA,CAAC7F,KAAa,EAAE8D,MAAc,EAAU;AACrD,EAAA,IAAA6B,YAAA,GAA4CV,WAAW,CAACjF,KAAK,CAAC;IAAA4F,aAAA,GAAAzD,cAAA,CAAAwD,YAAA,EAAA,CAAA,CAAA;AAAvDxB,IAAAA,GAAG,GAAAyB,aAAA,CAAA,CAAA,CAAA;AAAExB,IAAAA,UAAU,GAAAwB,aAAA,CAAA,CAAA,CAAA;AAAEvB,IAAAA,SAAS,GAAAuB,aAAA,CAAA,CAAA,CAAA;AAAEP,IAAAA,KAAK,GAAAO,aAAA,CAAA,CAAA,CAAA,CAAA;EACxC,OAAOL,IAAI,CAACpB,GAAG,EAAEC,UAAU,EAAEC,SAAS,GAAGP,MAAM,EAAEuB,KAAK,CAAC,CAAA;AACzD;;ACTA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,UAAUA,CAAC9F,KAAa,EAAE8D,MAAc,EAAU;AACzD,EAAA,IAAA6B,YAAA,GAAqBV,WAAW,CAACjF,KAAK,CAAC;IAAA4F,aAAA,GAAAzD,cAAA,CAAAwD,YAAA,EAAA,CAAA,CAAA;AAAhCvD,IAAAA,CAAC,GAAAwD,aAAA,CAAA,CAAA,CAAA;AAAEvD,IAAAA,CAAC,GAAAuD,aAAA,CAAA,CAAA,CAAA;AAAEtD,IAAAA,CAAC,GAAAsD,aAAA,CAAA,CAAA,CAAA;AAAErD,IAAAA,CAAC,GAAAqD,aAAA,CAAA,CAAA,CAAA,CAAA;EACjB,OAAOL,IAAI,CAACnD,CAAC,EAAEC,CAAC,GAAGyB,MAAM,EAAExB,CAAC,EAAEC,CAAC,CAAC,CAAA;AAClC;;ACXA;AACA;;AAEA;AACA;AACA;AACA,SAASwD,YAAYA,CAAC/F,KAAa,EAAU;AAC3C,EAAA,IAAIA,KAAK,KAAK,aAAa,EAAE,OAAO,CAAC,CAAA;EAErC,SAASgG,CAACA,CAACzE,CAAS,EAAE;AACpB,IAAA,IAAM0E,OAAO,GAAG1E,CAAC,GAAG,GAAG,CAAA;IACvB,OAAO0E,OAAO,IAAI,OAAO,GACrBA,OAAO,GAAG,KAAK,GACfzG,IAAI,CAAC0G,GAAG,CAAE,CAACD,OAAO,GAAG,KAAK,IAAI,KAAK,EAAG,GAAG,CAAC,CAAA;AAChD,GAAA;AAEA,EAAA,IAAAE,YAAA,GAAkB7F,WAAW,CAACN,KAAK,CAAC;IAAAoG,aAAA,GAAAjE,cAAA,CAAAgE,YAAA,EAAA,CAAA,CAAA;AAA7B1E,IAAAA,CAAC,GAAA2E,aAAA,CAAA,CAAA,CAAA;AAAEC,IAAAA,CAAC,GAAAD,aAAA,CAAA,CAAA,CAAA;AAAEE,IAAAA,CAAC,GAAAF,aAAA,CAAA,CAAA,CAAA,CAAA;AACd,EAAA,OAAO,MAAM,GAAGJ,CAAC,CAACvE,CAAC,CAAC,GAAG,MAAM,GAAGuE,CAAC,CAACK,CAAC,CAAC,GAAG,MAAM,GAAGL,CAAC,CAACM,CAAC,CAAC,CAAA;AACtD;;ACnBA;AACA;;AAGA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,MAAc,EAAEC,MAAc,EAAU;AAC3D,EAAA,IAAMC,UAAU,GAAGX,YAAY,CAACS,MAAM,CAAC,CAAA;AACvC,EAAA,IAAMG,UAAU,GAAGZ,YAAY,CAACU,MAAM,CAAC,CAAA;EAEvC,OAAOC,UAAU,GAAGC,UAAU,GAC1B,CAACD,UAAU,GAAG,IAAI,KAAKC,UAAU,GAAG,IAAI,CAAC,GACzC,CAACA,UAAU,GAAG,IAAI,KAAKD,UAAU,GAAG,IAAI,CAAC,CAAA;AAC/C;;ACbA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,IAAIA,CAAClC,GAAW,EAAEC,KAAa,EAAEC,IAAY,EAAES,KAAa,EAAU;EAC7E,OAAQ,CAAA,KAAA,EAAOjG,KAAK,CAAC,CAAC,EAAE,GAAG,EAAEsF,GAAG,CAAC,CAACc,OAAO,EAAG,KAAIpG,KAAK,CACnD,CAAC,EACD,GAAG,EACHuF,KACF,CAAC,CAACa,OAAO,EAAG,CAAIpG,EAAAA,EAAAA,KAAK,CAAC,CAAC,EAAE,GAAG,EAAEwF,IAAI,CAAC,CAACY,OAAO,EAAG,CAAI1D,EAAAA,EAAAA,UAAU,CAC1D1C,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEiG,KAAK,CAAC,CAACG,OAAO,CAAC,CAAC,CAC9B,CAAE,CAAE,CAAA,CAAA,CAAA;AACN;;ACfA;AACA;AACA;AACA,SAASqB,GAAGA,CAACL,MAAc,EAAEC,MAAc,EAAEK,MAAc,EAAU;AACnE,EAAA,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAIC,CAAS,EAAE7B,KAAa,EAAA;AAAA,IAAA;AACzC;AACAA,MAAAA,KAAK,KAAK,CAAC,GAAG6B,CAAC,GAAGA,CAAC,GAAG,GAAA;AAAG,MAAA;AAAA,GAAA,CAAA;EAE3B,IAAA9B,gBAAA,GAAyB5E,WAAW,CAACkG,MAAM,CAAC,CAAClF,GAAG,CAACyF,SAAS,CAAC;IAAA3B,iBAAA,GAAAjD,cAAA,CAAA+C,gBAAA,EAAA,CAAA,CAAA;AAApD+B,IAAAA,EAAE,GAAA7B,iBAAA,CAAA,CAAA,CAAA;AAAE8B,IAAAA,EAAE,GAAA9B,iBAAA,CAAA,CAAA,CAAA;AAAE+B,IAAAA,EAAE,GAAA/B,iBAAA,CAAA,CAAA,CAAA;AAAEgC,IAAAA,EAAE,GAAAhC,iBAAA,CAAA,CAAA,CAAA,CAAA;EACrB,IAAAiC,iBAAA,GAAyB/G,WAAW,CAACmG,MAAM,CAAC,CAACnF,GAAG,CAACyF,SAAS,CAAC;IAAAO,iBAAA,GAAAnF,cAAA,CAAAkF,iBAAA,EAAA,CAAA,CAAA;AAApDE,IAAAA,EAAE,GAAAD,iBAAA,CAAA,CAAA,CAAA;AAAEE,IAAAA,EAAE,GAAAF,iBAAA,CAAA,CAAA,CAAA;AAAEG,IAAAA,EAAE,GAAAH,iBAAA,CAAA,CAAA,CAAA;AAAEI,IAAAA,EAAE,GAAAJ,iBAAA,CAAA,CAAA,CAAA,CAAA;;AAErB;AACA;AACA,EAAA,IAAMK,UAAU,GAAGD,EAAE,GAAGN,EAAE,CAAA;AAC1B,EAAA,IAAMQ,gBAAgB,GAAGd,MAAM,GAAG,CAAC,GAAG,CAAC,CAAA;EACvC,IAAMe,cAAc,GAClBD,gBAAgB,GAAGD,UAAU,KAAK,CAAC,CAAC,GAChCC,gBAAgB,GAChBA,gBAAgB,GAAGD,UAAU,IAAI,CAAC,GAAGC,gBAAgB,GAAGD,UAAU,CAAC,CAAA;AACzE,EAAA,IAAMG,OAAO,GAAG,CAACD,cAAc,GAAG,CAAC,IAAI,CAAC,CAAA;AACxC,EAAA,IAAME,OAAO,GAAG,CAAC,GAAGD,OAAO,CAAA;EAE3B,IAAMrG,CAAC,GAAG,CAACwF,EAAE,GAAGc,OAAO,GAAGR,EAAE,GAAGO,OAAO,IAAI,GAAG,CAAA;EAC7C,IAAMzB,CAAC,GAAG,CAACa,EAAE,GAAGa,OAAO,GAAGP,EAAE,GAAGM,OAAO,IAAI,GAAG,CAAA;EAC7C,IAAMxB,CAAC,GAAG,CAACa,EAAE,GAAGY,OAAO,GAAGN,EAAE,GAAGK,OAAO,IAAI,GAAG,CAAA;EAC7C,IAAMvF,CAAC,GAAGmF,EAAE,GAAGZ,MAAM,GAAGM,EAAE,IAAI,CAAC,GAAGN,MAAM,CAAC,CAAA;EAEzC,OAAOF,IAAI,CAACnF,CAAC,EAAE4E,CAAC,EAAEC,CAAC,EAAE/D,CAAC,CAAC,CAAA;AACzB;;AC5BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASyF,QAAQA,GAA6C;AAAA,EAAA,KAAA,IAAAC,IAAA,GAAAC,SAAA,CAAApF,MAAA,EAAzCqF,MAAM,GAAAlH,IAAAA,KAAA,CAAAgH,IAAA,GAAAG,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA,EAAA,EAAA;AAAND,IAAAA,MAAM,CAAAC,IAAA,CAAAF,GAAAA,SAAA,CAAAE,IAAA,CAAA,CAAA;AAAA,GAAA;EACzB,OAAO,UAACpB,CAAC,EAAK;AACZ,IAAA,IAAMqB,SAAS,GAAGF,MAAM,CAACrF,MAAM,GAAG,CAAC,CAAA;AACnC,IAAA,IAAMwF,QAAQ,GAAGlJ,KAAK,CAAC,CAAC,EAAEiJ,SAAS,EAAE7I,IAAI,CAAC+I,KAAK,CAACvB,CAAC,GAAGqB,SAAS,CAAC,CAAC,CAAA;AAC/D,IAAA,IAAMG,SAAS,GAAGpJ,KAAK,CAAC,CAAC,EAAEiJ,SAAS,EAAE7I,IAAI,CAACiJ,IAAI,CAACzB,CAAC,GAAGqB,SAAS,CAAC,CAAC,CAAA;AAE/D,IAAA,IAAM7B,MAAM,GAAG2B,MAAM,CAACG,QAAQ,CAAC,CAAA;AAC/B,IAAA,IAAM7B,MAAM,GAAG0B,MAAM,CAACK,SAAS,CAAC,CAAA;AAEhC,IAAA,IAAME,IAAI,GAAG,CAAC,GAAGL,SAAS,CAAA;IAC1B,IAAMvB,MAAM,GAAG,CAACE,CAAC,GAAG0B,IAAI,GAAGJ,QAAQ,IAAII,IAAI,CAAA;AAE3C,IAAA,OAAO7B,GAAG,CAACL,MAAM,EAAEC,MAAM,EAAEK,MAAM,CAAC,CAAA;GACnC,CAAA;AACH;;ACvCA,IAAM6B,UAAU,GAAG;AACjBC,EAAAA,UAAU,EAAE,GAAG;AACfC,EAAAA,QAAQ,EAAE,CAAC;AACXC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,GAAG,EAAE,CAAA;AACP,CAAC,CAAA;;AAED;AACA;AACA;AACA;AACA,SAASC,cAAcA,CACrBhJ,KAAa,EAGJ;AAAA,EAAA,IAFTiJ,QAAkD,GAAAf,SAAA,CAAApF,MAAA,GAAA,CAAA,IAAAoF,SAAA,CAAA,CAAA,CAAA,KAAAgB,SAAA,GAAAhB,SAAA,CAAA,CAAA,CAAA,GAAG,IAAI,CAAA;AAAA,EAAA,IACzDiB,UAAkB,GAAAjB,SAAA,CAAApF,MAAA,GAAA,CAAA,IAAAoF,SAAA,CAAA,CAAA,CAAA,KAAAgB,SAAA,GAAAhB,SAAA,CAAA,CAAA,CAAA,GAAG,MAAM,CAAA;EAE3B,OAAO3B,WAAW,CAACvG,KAAK,EAAEmJ,UAAU,CAAC,GAAGR,UAAU,CAACM,QAAQ,CAAC,CAAA;AAC9D;;AClBA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,OAAOA,CAACpJ,KAAa,EAAE8D,MAAc,EAAU;AACtD,EAAA,OAAO+B,MAAM,CAAC7F,KAAK,EAAE,CAAC8D,MAAM,CAAC,CAAA;AAC/B;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuF,cAAcA,CAACrJ,KAAa,EAAE8D,MAAc,EAAU;AAC7D,EAAA,IAAAqC,YAAA,GAAqB7F,WAAW,CAACN,KAAK,CAAC;IAAAoG,aAAA,GAAAjE,cAAA,CAAAgE,YAAA,EAAA,CAAA,CAAA;AAAhC1E,IAAAA,CAAC,GAAA2E,aAAA,CAAA,CAAA,CAAA;AAAEC,IAAAA,CAAC,GAAAD,aAAA,CAAA,CAAA,CAAA;AAAEE,IAAAA,CAAC,GAAAF,aAAA,CAAA,CAAA,CAAA;AAAE7D,IAAAA,CAAC,GAAA6D,aAAA,CAAA,CAAA,CAAA,CAAA;EACjB,OAAOQ,IAAI,CAACnF,CAAC,EAAE4E,CAAC,EAAEC,CAAC,EAAE/D,CAAC,GAAGuB,MAAM,CAAC,CAAA;AAClC;;ACVA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwF,OAAOA,CAACtJ,KAAa,EAAE8D,MAAc,EAAU;AACtD,EAAA,OAAOuF,cAAc,CAACrJ,KAAK,EAAE,CAAC8D,MAAM,CAAC,CAAA;AACvC;;ACRA;AACA;AACA;AACA;AACA;AACA,SAASyF,oBAAoBA,CAACvJ,KAAa,EAAW;AACpD,EAAA,OAAO+F,YAAY,CAAC/F,KAAK,CAAC,GAAG,KAAK,CAAA;AACpC;;ACPA;AACA;AACA;AACA;AACA,SAASwJ,aAAaA,CAACxJ,KAAa,EAAU;AAC5C,EAAA,OAAOuJ,oBAAoB,CAACvJ,KAAK,CAAC,GAAG,MAAM,GAAG,MAAM,CAAA;AACtD;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASyJ,QAAQA,CAACzJ,KAAa,EAAE8D,MAAc,EAAU;AACvD,EAAA,OAAOgC,UAAU,CAAC9F,KAAK,EAAE,CAAC8D,MAAM,CAAC,CAAA;AACnC;;ACRA;AACA;AACA;AACA,SAAS4F,KAAKA,CAAC1J,KAAa,EAAU;AACpC,EAAA,IAAAmG,YAAA,GAAqB7F,WAAW,CAACN,KAAK,CAAC;IAAAoG,aAAA,GAAAjE,cAAA,CAAAgE,YAAA,EAAA,CAAA,CAAA;AAAhC1E,IAAAA,CAAC,GAAA2E,aAAA,CAAA,CAAA,CAAA;AAAEC,IAAAA,CAAC,GAAAD,aAAA,CAAA,CAAA,CAAA;AAAEE,IAAAA,CAAC,GAAAF,aAAA,CAAA,CAAA,CAAA;AAAE7D,IAAAA,CAAC,GAAA6D,aAAA,CAAA,CAAA,CAAA,CAAA;AAEjB,EAAA,IAAI3C,GAAG,GAAG,SAANA,GAAGA,CAAIlC,CAAS,EAAK;AACvB,IAAA,IAAMa,CAAC,GAAGhD,KAAK,CAAC,CAAC,EAAE,GAAG,EAAEmC,CAAC,CAAC,CAACmC,QAAQ,CAAC,EAAE,CAAC,CAAA;AACvC;AACA;IACA,OAAOtB,CAAC,CAACU,MAAM,KAAK,CAAC,GAAI,CAAGV,CAAAA,EAAAA,CAAE,CAAC,CAAA,GAAGA,CAAC,CAAA;GACpC,CAAA;AAED,EAAA,OAAQ,CAAGqB,CAAAA,EAAAA,GAAG,CAAChC,CAAC,CAAE,CAAEgC,EAAAA,GAAG,CAAC4C,CAAC,CAAE,CAAA,EAAE5C,GAAG,CAAC6C,CAAC,CAAE,CAAA,EAAE/D,CAAC,GAAG,CAAC,GAAGkB,GAAG,CAACjE,IAAI,CAAC0E,KAAK,CAAC3B,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAG,CAAC,CAAA,CAAA;AAC/E;;ACdA;AACA;AACA;AACA,SAASoH,MAAMA,CAAC3J,KAAa,EAAU;EACrC,OAAO4G,IAAI,CAAAgD,KAAA,CAAAvI,KAAAA,CAAAA,EAAAA,kBAAA,CAAIf,WAAW,CAACN,KAAK,CAAC,CAAC,CAAA,CAAA;AACpC;;ACLA;AACA;AACA;AACA,SAAS6J,MAAMA,CAAC7J,KAAa,EAAU;EACrC,OAAOuF,IAAI,CAAAqE,KAAA,CAAAvI,KAAAA,CAAAA,EAAAA,kBAAA,CAAI4D,WAAW,CAACjF,KAAK,CAAC,CAAC,CAAA,CAAA;AACpC;;;;;;;;;;;;;;;;;;;;;;;;;;"}