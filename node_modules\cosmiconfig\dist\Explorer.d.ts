import { ExplorerBase } from './ExplorerBase';
import { CosmiconfigResult, ExplorerOptions } from './types';
declare class Explorer extends ExplorerBase<ExplorerOptions> {
    constructor(options: ExplorerOptions);
    search(searchFrom?: string): Promise<CosmiconfigResult>;
    private searchFromDirectory;
    private searchDirectory;
    private loadSearchPlace;
    private loadFileContent;
    private createCosmiconfigResult;
    load(filepath: string): Promise<CosmiconfigResult>;
}
export { Explorer };
//# sourceMappingURL=Explorer.d.ts.map