{"version": 3, "sources": ["../src/readFile.ts"], "names": ["fsReadFileAsync", "pathname", "encoding", "Promise", "resolve", "reject", "fs", "readFile", "error", "contents", "filepath", "options", "throwNotFound", "content", "code", "readFileSync"], "mappings": ";;;;;;;;AAAA;;;;AAEA,eAAeA,eAAf,CACEC,QADF,EAEEC,QAFF,EAGmB;AACjB,SAAO,IAAIC,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAA2B;AAC5CC,gBAAGC,QAAH,CAAYN,QAAZ,EAAsBC,QAAtB,EAAgC,CAACM,KAAD,EAAQC,QAAR,KAA2B;AACzD,UAAID,KAAJ,EAAW;AACTH,QAAAA,MAAM,CAACG,KAAD,CAAN;AACA;AACD;;AAEDJ,MAAAA,OAAO,CAACK,QAAD,CAAP;AACD,KAPD;AAQD,GATM,CAAP;AAUD;;AAMD,eAAeF,QAAf,CACEG,QADF,EAEEC,OAAgB,GAAG,EAFrB,EAG0B;AACxB,QAAMC,aAAa,GAAGD,OAAO,CAACC,aAAR,KAA0B,IAAhD;;AAEA,MAAI;AACF,UAAMC,OAAO,GAAG,MAAMb,eAAe,CAACU,QAAD,EAAW,MAAX,CAArC;AAEA,WAAOG,OAAP;AACD,GAJD,CAIE,OAAOL,KAAP,EAAc;AACd,QACEI,aAAa,KAAK,KAAlB,KACCJ,KAAK,CAACM,IAAN,KAAe,QAAf,IAA2BN,KAAK,CAACM,IAAN,KAAe,QAD3C,CADF,EAGE;AACA,aAAO,IAAP;AACD;;AAED,UAAMN,KAAN;AACD;AACF;;AAED,SAASO,YAAT,CAAsBL,QAAtB,EAAwCC,OAAgB,GAAG,EAA3D,EAA8E;AAC5E,QAAMC,aAAa,GAAGD,OAAO,CAACC,aAAR,KAA0B,IAAhD;;AAEA,MAAI;AACF,UAAMC,OAAO,GAAGP,YAAGS,YAAH,CAAgBL,QAAhB,EAA0B,MAA1B,CAAhB;;AAEA,WAAOG,OAAP;AACD,GAJD,CAIE,OAAOL,KAAP,EAAc;AACd,QACEI,aAAa,KAAK,KAAlB,KACCJ,KAAK,CAACM,IAAN,KAAe,QAAf,IAA2BN,KAAK,CAACM,IAAN,KAAe,QAD3C,CADF,EAGE;AACA,aAAO,IAAP;AACD;;AAED,UAAMN,KAAN;AACD;AACF", "sourcesContent": ["import fs from 'fs';\n\nasync function fsReadFileAsync(\n  pathname: string,\n  encoding: BufferEncoding,\n): Promise<string> {\n  return new Promise((resolve, reject): void => {\n    fs.readFile(pathname, encoding, (error, contents): void => {\n      if (error) {\n        reject(error);\n        return;\n      }\n\n      resolve(contents);\n    });\n  });\n}\n\ninterface Options {\n  throwNotFound?: boolean;\n}\n\nasync function readFile(\n  filepath: string,\n  options: Options = {},\n): Promise<string | null> {\n  const throwNotFound = options.throwNotFound === true;\n\n  try {\n    const content = await fsReadFileAsync(filepath, 'utf8');\n\n    return content;\n  } catch (error) {\n    if (\n      throwNotFound === false &&\n      (error.code === 'ENOENT' || error.code === 'EISDIR')\n    ) {\n      return null;\n    }\n\n    throw error;\n  }\n}\n\nfunction readFileSync(filepath: string, options: Options = {}): string | null {\n  const throwNotFound = options.throwNotFound === true;\n\n  try {\n    const content = fs.readFileSync(filepath, 'utf8');\n\n    return content;\n  } catch (error) {\n    if (\n      throwNotFound === false &&\n      (error.code === 'ENOENT' || error.code === 'EISDIR')\n    ) {\n      return null;\n    }\n\n    throw error;\n  }\n}\n\nexport { readFile, readFileSync };\n"], "file": "readFile.js"}