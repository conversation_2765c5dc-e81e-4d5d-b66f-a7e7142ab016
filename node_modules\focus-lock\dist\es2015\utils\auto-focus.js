import { filterAutoFocusable } from './DOMutils';
import { pickFirstFocus } from './firstFocus';
import { getDataset } from './is';
var findAutoFocused = function (autoFocusables) {
    return function (node) {
        var _a;
        var autofocus = (_a = getDataset(node)) === null || _a === void 0 ? void 0 : _a.autofocus;
        return (
        // @ts-expect-error
        node.autofocus ||
            //
            (autofocus !== undefined && autofocus !== 'false') ||
            //
            autoFocusables.indexOf(node) >= 0);
    };
};
export var pickAutofocus = function (nodesIndexes, orderedNodes, groups) {
    var nodes = nodesIndexes.map(function (_a) {
        var node = _a.node;
        return node;
    });
    var autoFocusable = filterAutoFocusable(nodes.filter(findAutoFocused(groups)));
    if (autoFocusable && autoFocusable.length) {
        return pickFirstFocus(autoFocusable);
    }
    return pickFirstFocus(filterAutoFocusable(orderedNodes));
};
