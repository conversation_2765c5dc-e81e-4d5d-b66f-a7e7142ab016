/**
 * defines a focus group
 */
export declare const FOCUS_GROUP = "data-focus-lock";
/**
 * disables element discovery inside a group marked by key
 */
export declare const FOCUS_DISABLED = "data-focus-lock-disabled";
/**
 * allows uncontrolled focus within the marked area, effectively disabling focus lock for it's content
 */
export declare const FOCUS_ALLOW = "data-no-focus-lock";
/**
 * instructs autofocus engine to pick default autofocus inside a given node
 * can be set on the element or container
 */
export declare const FOCUS_AUTO = "data-autofocus-inside";
/**
 * instructs autofocus to ignore elements within a given node
 * can be set on the element or container
 */
export declare const FOCUS_NO_AUTOFOCUS = "data-no-autofocus";
