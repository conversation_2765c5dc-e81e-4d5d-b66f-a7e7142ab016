/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 12h.01", key: "1mp3jc" }],
  ["path", { d: "M16 12h.01", key: "1l6xoz" }],
  ["path", { d: "m17 7 5 5-5 5", key: "1xlxn0" }],
  ["path", { d: "m7 7-5 5 5 5", key: "19njba" }],
  ["path", { d: "M8 12h.01", key: "czm47f" }]
];
const ChevronsLeftRightEllipsis = createLucideIcon("chevrons-left-right-ellipsis", __iconNode);

export { __iconNode, ChevronsLeftRightEllipsis as default };
//# sourceMappingURL=chevrons-left-right-ellipsis.js.map
