/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4", key: "1pf5j1" }],
  ["path", { d: "M14 2v4a2 2 0 0 0 2 2h4", key: "tnqrlb" }],
  ["path", { d: "m8 12.5-5 5", key: "b853mi" }],
  ["path", { d: "m3 12.5 5 5", key: "1qls4r" }]
];
const FileX2 = createLucideIcon("file-x-2", __iconNode);

export { __iconNode, FileX2 as default };
//# sourceMappingURL=file-x-2.js.map
