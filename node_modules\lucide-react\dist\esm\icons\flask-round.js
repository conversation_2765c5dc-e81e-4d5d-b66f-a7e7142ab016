/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 2v6.292a7 7 0 1 0 4 0V2", key: "1s42pc" }],
  ["path", { d: "M5 15h14", key: "m0yey3" }],
  ["path", { d: "M8.5 2h7", key: "csnxdl" }]
];
const FlaskRound = createLucideIcon("flask-round", __iconNode);

export { __iconNode, FlaskRound as default };
//# sourceMappingURL=flask-round.js.map
