/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "m3 10 2.5-2.5L3 5", key: "i6eama" }],
  ["path", { d: "m3 19 2.5-2.5L3 14", key: "w2gmor" }],
  ["path", { d: "M10 6h11", key: "c7qv1k" }],
  ["path", { d: "M10 12h11", key: "6m4ad9" }],
  ["path", { d: "M10 18h11", key: "11hvi2" }]
];
const ListCollapse = createLucideIcon("list-collapse", __iconNode);

export { __iconNode, ListCollapse as default };
//# sourceMappingURL=list-collapse.js.map
