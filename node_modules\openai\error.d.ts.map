{"version": 3, "file": "error.d.ts", "sourceRoot": "", "sources": ["src/error.ts"], "names": [], "mappings": "AAEA,OAAO,EAAe,OAAO,EAAE,MAAM,QAAQ,CAAC;AAE9C,qBAAa,WAAY,SAAQ,KAAK;CAAG;AAEzC,qBAAa,QAAQ,CACnB,OAAO,SAAS,MAAM,GAAG,SAAS,GAAG,MAAM,GAAG,SAAS,EACvD,QAAQ,SAAS,OAAO,GAAG,SAAS,GAAG,OAAO,GAAG,SAAS,EAC1D,MAAM,SAAS,MAAM,GAAG,SAAS,GAAG,MAAM,GAAG,SAAS,CACtD,SAAQ,WAAW;IACnB,yDAAyD;IACzD,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC;IACzB,0DAA0D;IAC1D,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC;IAC3B,sDAAsD;IACtD,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC;IAEvB,QAAQ,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACzC,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAC1C,QAAQ,CAAC,IAAI,EAAE,MAAM,GAAG,SAAS,CAAC;IAElC,QAAQ,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;gBAEnC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,SAAS,EAAE,OAAO,EAAE,QAAQ;IAa1F,OAAO,CAAC,MAAM,CAAC,WAAW;IAqB1B,MAAM,CAAC,QAAQ,CACb,MAAM,EAAE,MAAM,GAAG,SAAS,EAC1B,aAAa,EAAE,MAAM,GAAG,SAAS,EACjC,OAAO,EAAE,MAAM,GAAG,SAAS,EAC3B,OAAO,EAAE,OAAO,GAAG,SAAS,GAC3B,QAAQ;CAyCZ;AAED,qBAAa,iBAAkB,SAAQ,QAAQ,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;gBAClE,EAAE,OAAO,EAAE,GAAE;QAAE,OAAO,CAAC,EAAE,MAAM,CAAA;KAAO;CAGnD;AAED,qBAAa,kBAAmB,SAAQ,QAAQ,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;gBACnE,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;QAAE,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;QAAC,KAAK,CAAC,EAAE,KAAK,GAAG,SAAS,CAAA;KAAE;CAM5F;AAED,qBAAa,yBAA0B,SAAQ,kBAAkB;gBACnD,EAAE,OAAO,EAAE,GAAE;QAAE,OAAO,CAAC,EAAE,MAAM,CAAA;KAAO;CAGnD;AAED,qBAAa,eAAgB,SAAQ,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC;CAAG;AAE9D,qBAAa,mBAAoB,SAAQ,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC;CAAG;AAElE,qBAAa,qBAAsB,SAAQ,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC;CAAG;AAEpE,qBAAa,aAAc,SAAQ,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC;CAAG;AAE5D,qBAAa,aAAc,SAAQ,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC;CAAG;AAE5D,qBAAa,wBAAyB,SAAQ,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC;CAAG;AAEvE,qBAAa,cAAe,SAAQ,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC;CAAG;AAE7D,qBAAa,mBAAoB,SAAQ,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC;CAAG;AAErE,qBAAa,uBAAwB,SAAQ,WAAW;;CAIvD;AAED,qBAAa,8BAA+B,SAAQ,WAAW;;CAI9D"}