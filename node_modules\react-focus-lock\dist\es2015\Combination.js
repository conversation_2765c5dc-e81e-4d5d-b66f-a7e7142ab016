import _objectWithoutPropertiesLoose from "@babel/runtime/helpers/esm/objectWithoutPropertiesLoose";
import _extends from "@babel/runtime/helpers/esm/extends";
import React, { forwardRef } from 'react';
import FocusLockUI from './Lock';
import FocusTrap from './Trap';
var FocusLockCombination = /*#__PURE__*/forwardRef(function FocusLockUICombination(props, ref) {
  return /*#__PURE__*/React.createElement(FocusLockUI, _extends({
    sideCar: FocusTrap,
    ref: ref
  }, props));
});
var _ref = FocusLockUI.propTypes || {},
  sideCar = _ref.sideCar,
  propTypes = _objectWithoutPropertiesLoose(_ref, ["sideCar"]);
FocusLockCombination.propTypes = process.env.NODE_ENV !== "production" ? propTypes : {};
export default FocusLockCombination;