{"name": "ozon-price-optimizer-pro", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@chakra-ui/icons": "^2.1.1", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "axios": "^1.9.0", "body-parser": "^2.2.0", "chart.js": "^4.4.9", "cors": "^2.8.5", "csv-parser": "^3.2.0", "express": "^5.1.0", "framer-motion": "^10.18.0", "lucide-react": "^0.511.0", "multer": "^2.0.0", "openai": "^4.98.0", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-icons": "^5.0.1", "react-router-dom": "^6.21.3"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.33", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^5.0.8"}}