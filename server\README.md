# Ozon Price Optimizer Pro Bot

Telegram-бот для управления ценами на товары в Ozon и получения уведомлений о важных событиях.

## Возможности

- Авторизация пользователей по паролю
- Просмотр списка товаров
- Анализ товаров с помощью ИИ
- Изменение цен на товары
- Получение уведомлений об изменении цен
- Получение уведомлений о подозрительной активности конкурентов
- Получение рекомендаций по ценообразованию

## Установка и запуск

1. Установите зависимости:

```bash
npm install
```

2. Создайте файл `.env` и укажите в нем следующие параметры:

```
# Токен Telegram-бота (получите у @BotFather)
TELEGRAM_BOT_TOKEN=YOUR_TELEGRAM_BOT_TOKEN

# API-ключ OpenAI для ИИ-функциональности (опционально)
OPENAI_API_KEY=YOUR_OPENAI_API_KEY

# Пароль для доступа к боту
BOT_PASSWORD=ozonpro2025

# Порт для сервера
PORT=3000
```

3. Запустите сервер:

```bash
npm start
```

## Использование

1. Найдите бота в Telegram (по имени пользователя, которое вы указали при создании бота)
2. Отправьте команду `/start`
3. Введите пароль для авторизации (по умолчанию `ozonpro2025`)
4. После успешной авторизации вы получите свой Chat ID, который нужно указать в настройках приложения Ozon Price Optimizer Pro
5. Используйте команду `/help` для получения списка доступных команд

## Команды

- `/start` - Начать работу с ботом
- `/help` - Показать справку
- `/status` - Проверить статус подключения
- `/products` - Показать список товаров
- `/analyze [ID товара]` - Анализ товара с помощью ИИ
- `/price [ID товара] [новая цена]` - Изменить цену товара

## Интеграция с приложением

Для интеграции бота с приложением Ozon Price Optimizer Pro:

1. Запустите бота и авторизуйтесь
2. Получите свой Chat ID
3. Укажите Chat ID в настройках приложения (раздел "Настройки" -> "Telegram-бот")
4. Нажмите кнопку "Подключить Telegram-бота"
5. Отправьте тестовое сообщение, чтобы проверить работу бота

## API

Бот предоставляет API для отправки уведомлений:

### POST /api/notify

Отправляет уведомление пользователю.

Параметры запроса:

```json
{
  "chatId": "123456789",
  "type": "price_change",
  "data": {
    "product": {
      "name": "Название товара"
    },
    "oldPrice": 1000,
    "newPrice": 950,
    "reason": "Причина изменения цены"
  }
}
```

Типы уведомлений:

- `price_change` - Изменение цены товара
- `suspicious_activity` - Подозрительная активность конкурентов
- `recommended_price` - Рекомендация по цене

## Лицензия

MIT
