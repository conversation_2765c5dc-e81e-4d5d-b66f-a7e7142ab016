{"version": 3, "file": "abort-controller.umd.js", "sources": ["../node_modules/event-target-shim/dist/event-target-shim.mjs", "../src/abort-signal.ts", "../src/abort-controller.ts"], "sourcesContent": ["/**\n * <AUTHOR> <https://github.com/mysticatea>\n * @copyright 2015 Toru Nagashima. All rights reserved.\n * See LICENSE file in root directory for full license.\n */\n/**\n * @typedef {object} PrivateData\n * @property {EventTarget} eventTarget The event target.\n * @property {{type:string}} event The original event object.\n * @property {number} eventPhase The current event phase.\n * @property {EventTarget|null} currentTarget The current event target.\n * @property {boolean} canceled The flag to prevent default.\n * @property {boolean} stopped The flag to stop propagation.\n * @property {boolean} immediateStopped The flag to stop propagation immediately.\n * @property {Function|null} passiveListener The listener if the current listener is passive. Otherwise this is null.\n * @property {number} timeStamp The unix time.\n * @private\n */\n\n/**\n * Private data for event wrappers.\n * @type {WeakMap<Event, PrivateData>}\n * @private\n */\nconst privateData = new WeakMap();\n\n/**\n * Cache for wrapper classes.\n * @type {WeakMap<Object, Function>}\n * @private\n */\nconst wrappers = new WeakMap();\n\n/**\n * Get private data.\n * @param {Event} event The event object to get private data.\n * @returns {PrivateData} The private data of the event.\n * @private\n */\nfunction pd(event) {\n    const retv = privateData.get(event);\n    console.assert(\n        retv != null,\n        \"'this' is expected an Event object, but got\",\n        event\n    );\n    return retv\n}\n\n/**\n * https://dom.spec.whatwg.org/#set-the-canceled-flag\n * @param data {PrivateData} private data.\n */\nfunction setCancelFlag(data) {\n    if (data.passiveListener != null) {\n        if (\n            typeof console !== \"undefined\" &&\n            typeof console.error === \"function\"\n        ) {\n            console.error(\n                \"Unable to preventDefault inside passive event listener invocation.\",\n                data.passiveListener\n            );\n        }\n        return\n    }\n    if (!data.event.cancelable) {\n        return\n    }\n\n    data.canceled = true;\n    if (typeof data.event.preventDefault === \"function\") {\n        data.event.preventDefault();\n    }\n}\n\n/**\n * @see https://dom.spec.whatwg.org/#interface-event\n * @private\n */\n/**\n * The event wrapper.\n * @constructor\n * @param {EventTarget} eventTarget The event target of this dispatching.\n * @param {Event|{type:string}} event The original event to wrap.\n */\nfunction Event(eventTarget, event) {\n    privateData.set(this, {\n        eventTarget,\n        event,\n        eventPhase: 2,\n        currentTarget: eventTarget,\n        canceled: false,\n        stopped: false,\n        immediateStopped: false,\n        passiveListener: null,\n        timeStamp: event.timeStamp || Date.now(),\n    });\n\n    // https://heycam.github.io/webidl/#Unforgeable\n    Object.defineProperty(this, \"isTrusted\", { value: false, enumerable: true });\n\n    // Define accessors\n    const keys = Object.keys(event);\n    for (let i = 0; i < keys.length; ++i) {\n        const key = keys[i];\n        if (!(key in this)) {\n            Object.defineProperty(this, key, defineRedirectDescriptor(key));\n        }\n    }\n}\n\n// Should be enumerable, but class methods are not enumerable.\nEvent.prototype = {\n    /**\n     * The type of this event.\n     * @type {string}\n     */\n    get type() {\n        return pd(this).event.type\n    },\n\n    /**\n     * The target of this event.\n     * @type {EventTarget}\n     */\n    get target() {\n        return pd(this).eventTarget\n    },\n\n    /**\n     * The target of this event.\n     * @type {EventTarget}\n     */\n    get currentTarget() {\n        return pd(this).currentTarget\n    },\n\n    /**\n     * @returns {EventTarget[]} The composed path of this event.\n     */\n    composedPath() {\n        const currentTarget = pd(this).currentTarget;\n        if (currentTarget == null) {\n            return []\n        }\n        return [currentTarget]\n    },\n\n    /**\n     * Constant of NONE.\n     * @type {number}\n     */\n    get NONE() {\n        return 0\n    },\n\n    /**\n     * Constant of CAPTURING_PHASE.\n     * @type {number}\n     */\n    get CAPTURING_PHASE() {\n        return 1\n    },\n\n    /**\n     * Constant of AT_TARGET.\n     * @type {number}\n     */\n    get AT_TARGET() {\n        return 2\n    },\n\n    /**\n     * Constant of BUBBLING_PHASE.\n     * @type {number}\n     */\n    get BUBBLING_PHASE() {\n        return 3\n    },\n\n    /**\n     * The target of this event.\n     * @type {number}\n     */\n    get eventPhase() {\n        return pd(this).eventPhase\n    },\n\n    /**\n     * Stop event bubbling.\n     * @returns {void}\n     */\n    stopPropagation() {\n        const data = pd(this);\n\n        data.stopped = true;\n        if (typeof data.event.stopPropagation === \"function\") {\n            data.event.stopPropagation();\n        }\n    },\n\n    /**\n     * Stop event bubbling.\n     * @returns {void}\n     */\n    stopImmediatePropagation() {\n        const data = pd(this);\n\n        data.stopped = true;\n        data.immediateStopped = true;\n        if (typeof data.event.stopImmediatePropagation === \"function\") {\n            data.event.stopImmediatePropagation();\n        }\n    },\n\n    /**\n     * The flag to be bubbling.\n     * @type {boolean}\n     */\n    get bubbles() {\n        return Boolean(pd(this).event.bubbles)\n    },\n\n    /**\n     * The flag to be cancelable.\n     * @type {boolean}\n     */\n    get cancelable() {\n        return Boolean(pd(this).event.cancelable)\n    },\n\n    /**\n     * Cancel this event.\n     * @returns {void}\n     */\n    preventDefault() {\n        setCancelFlag(pd(this));\n    },\n\n    /**\n     * The flag to indicate cancellation state.\n     * @type {boolean}\n     */\n    get defaultPrevented() {\n        return pd(this).canceled\n    },\n\n    /**\n     * The flag to be composed.\n     * @type {boolean}\n     */\n    get composed() {\n        return Boolean(pd(this).event.composed)\n    },\n\n    /**\n     * The unix time of this event.\n     * @type {number}\n     */\n    get timeStamp() {\n        return pd(this).timeStamp\n    },\n\n    /**\n     * The target of this event.\n     * @type {EventTarget}\n     * @deprecated\n     */\n    get srcElement() {\n        return pd(this).eventTarget\n    },\n\n    /**\n     * The flag to stop event bubbling.\n     * @type {boolean}\n     * @deprecated\n     */\n    get cancelBubble() {\n        return pd(this).stopped\n    },\n    set cancelBubble(value) {\n        if (!value) {\n            return\n        }\n        const data = pd(this);\n\n        data.stopped = true;\n        if (typeof data.event.cancelBubble === \"boolean\") {\n            data.event.cancelBubble = true;\n        }\n    },\n\n    /**\n     * The flag to indicate cancellation state.\n     * @type {boolean}\n     * @deprecated\n     */\n    get returnValue() {\n        return !pd(this).canceled\n    },\n    set returnValue(value) {\n        if (!value) {\n            setCancelFlag(pd(this));\n        }\n    },\n\n    /**\n     * Initialize this event object. But do nothing under event dispatching.\n     * @param {string} type The event type.\n     * @param {boolean} [bubbles=false] The flag to be possible to bubble up.\n     * @param {boolean} [cancelable=false] The flag to be possible to cancel.\n     * @deprecated\n     */\n    initEvent() {\n        // Do nothing.\n    },\n};\n\n// `constructor` is not enumerable.\nObject.defineProperty(Event.prototype, \"constructor\", {\n    value: Event,\n    configurable: true,\n    writable: true,\n});\n\n// Ensure `event instanceof window.Event` is `true`.\nif (typeof window !== \"undefined\" && typeof window.Event !== \"undefined\") {\n    Object.setPrototypeOf(Event.prototype, window.Event.prototype);\n\n    // Make association for wrappers.\n    wrappers.set(window.Event.prototype, Event);\n}\n\n/**\n * Get the property descriptor to redirect a given property.\n * @param {string} key Property name to define property descriptor.\n * @returns {PropertyDescriptor} The property descriptor to redirect the property.\n * @private\n */\nfunction defineRedirectDescriptor(key) {\n    return {\n        get() {\n            return pd(this).event[key]\n        },\n        set(value) {\n            pd(this).event[key] = value;\n        },\n        configurable: true,\n        enumerable: true,\n    }\n}\n\n/**\n * Get the property descriptor to call a given method property.\n * @param {string} key Property name to define property descriptor.\n * @returns {PropertyDescriptor} The property descriptor to call the method property.\n * @private\n */\nfunction defineCallDescriptor(key) {\n    return {\n        value() {\n            const event = pd(this).event;\n            return event[key].apply(event, arguments)\n        },\n        configurable: true,\n        enumerable: true,\n    }\n}\n\n/**\n * Define new wrapper class.\n * @param {Function} BaseEvent The base wrapper class.\n * @param {Object} proto The prototype of the original event.\n * @returns {Function} The defined wrapper class.\n * @private\n */\nfunction defineWrapper(BaseEvent, proto) {\n    const keys = Object.keys(proto);\n    if (keys.length === 0) {\n        return BaseEvent\n    }\n\n    /** CustomEvent */\n    function CustomEvent(eventTarget, event) {\n        BaseEvent.call(this, eventTarget, event);\n    }\n\n    CustomEvent.prototype = Object.create(BaseEvent.prototype, {\n        constructor: { value: CustomEvent, configurable: true, writable: true },\n    });\n\n    // Define accessors.\n    for (let i = 0; i < keys.length; ++i) {\n        const key = keys[i];\n        if (!(key in BaseEvent.prototype)) {\n            const descriptor = Object.getOwnPropertyDescriptor(proto, key);\n            const isFunc = typeof descriptor.value === \"function\";\n            Object.defineProperty(\n                CustomEvent.prototype,\n                key,\n                isFunc\n                    ? defineCallDescriptor(key)\n                    : defineRedirectDescriptor(key)\n            );\n        }\n    }\n\n    return CustomEvent\n}\n\n/**\n * Get the wrapper class of a given prototype.\n * @param {Object} proto The prototype of the original event to get its wrapper.\n * @returns {Function} The wrapper class.\n * @private\n */\nfunction getWrapper(proto) {\n    if (proto == null || proto === Object.prototype) {\n        return Event\n    }\n\n    let wrapper = wrappers.get(proto);\n    if (wrapper == null) {\n        wrapper = defineWrapper(getWrapper(Object.getPrototypeOf(proto)), proto);\n        wrappers.set(proto, wrapper);\n    }\n    return wrapper\n}\n\n/**\n * Wrap a given event to management a dispatching.\n * @param {EventTarget} eventTarget The event target of this dispatching.\n * @param {Object} event The event to wrap.\n * @returns {Event} The wrapper instance.\n * @private\n */\nfunction wrapEvent(eventTarget, event) {\n    const Wrapper = getWrapper(Object.getPrototypeOf(event));\n    return new Wrapper(eventTarget, event)\n}\n\n/**\n * Get the immediateStopped flag of a given event.\n * @param {Event} event The event to get.\n * @returns {boolean} The flag to stop propagation immediately.\n * @private\n */\nfunction isStopped(event) {\n    return pd(event).immediateStopped\n}\n\n/**\n * Set the current event phase of a given event.\n * @param {Event} event The event to set current target.\n * @param {number} eventPhase New event phase.\n * @returns {void}\n * @private\n */\nfunction setEventPhase(event, eventPhase) {\n    pd(event).eventPhase = eventPhase;\n}\n\n/**\n * Set the current target of a given event.\n * @param {Event} event The event to set current target.\n * @param {EventTarget|null} currentTarget New current target.\n * @returns {void}\n * @private\n */\nfunction setCurrentTarget(event, currentTarget) {\n    pd(event).currentTarget = currentTarget;\n}\n\n/**\n * Set a passive listener of a given event.\n * @param {Event} event The event to set current target.\n * @param {Function|null} passiveListener New passive listener.\n * @returns {void}\n * @private\n */\nfunction setPassiveListener(event, passiveListener) {\n    pd(event).passiveListener = passiveListener;\n}\n\n/**\n * @typedef {object} ListenerNode\n * @property {Function} listener\n * @property {1|2|3} listenerType\n * @property {boolean} passive\n * @property {boolean} once\n * @property {ListenerNode|null} next\n * @private\n */\n\n/**\n * @type {WeakMap<object, Map<string, ListenerNode>>}\n * @private\n */\nconst listenersMap = new WeakMap();\n\n// Listener types\nconst CAPTURE = 1;\nconst BUBBLE = 2;\nconst ATTRIBUTE = 3;\n\n/**\n * Check whether a given value is an object or not.\n * @param {any} x The value to check.\n * @returns {boolean} `true` if the value is an object.\n */\nfunction isObject(x) {\n    return x !== null && typeof x === \"object\" //eslint-disable-line no-restricted-syntax\n}\n\n/**\n * Get listeners.\n * @param {EventTarget} eventTarget The event target to get.\n * @returns {Map<string, ListenerNode>} The listeners.\n * @private\n */\nfunction getListeners(eventTarget) {\n    const listeners = listenersMap.get(eventTarget);\n    if (listeners == null) {\n        throw new TypeError(\n            \"'this' is expected an EventTarget object, but got another value.\"\n        )\n    }\n    return listeners\n}\n\n/**\n * Get the property descriptor for the event attribute of a given event.\n * @param {string} eventName The event name to get property descriptor.\n * @returns {PropertyDescriptor} The property descriptor.\n * @private\n */\nfunction defineEventAttributeDescriptor(eventName) {\n    return {\n        get() {\n            const listeners = getListeners(this);\n            let node = listeners.get(eventName);\n            while (node != null) {\n                if (node.listenerType === ATTRIBUTE) {\n                    return node.listener\n                }\n                node = node.next;\n            }\n            return null\n        },\n\n        set(listener) {\n            if (typeof listener !== \"function\" && !isObject(listener)) {\n                listener = null; // eslint-disable-line no-param-reassign\n            }\n            const listeners = getListeners(this);\n\n            // Traverse to the tail while removing old value.\n            let prev = null;\n            let node = listeners.get(eventName);\n            while (node != null) {\n                if (node.listenerType === ATTRIBUTE) {\n                    // Remove old value.\n                    if (prev !== null) {\n                        prev.next = node.next;\n                    } else if (node.next !== null) {\n                        listeners.set(eventName, node.next);\n                    } else {\n                        listeners.delete(eventName);\n                    }\n                } else {\n                    prev = node;\n                }\n\n                node = node.next;\n            }\n\n            // Add new value.\n            if (listener !== null) {\n                const newNode = {\n                    listener,\n                    listenerType: ATTRIBUTE,\n                    passive: false,\n                    once: false,\n                    next: null,\n                };\n                if (prev === null) {\n                    listeners.set(eventName, newNode);\n                } else {\n                    prev.next = newNode;\n                }\n            }\n        },\n        configurable: true,\n        enumerable: true,\n    }\n}\n\n/**\n * Define an event attribute (e.g. `eventTarget.onclick`).\n * @param {Object} eventTargetPrototype The event target prototype to define an event attrbite.\n * @param {string} eventName The event name to define.\n * @returns {void}\n */\nfunction defineEventAttribute(eventTargetPrototype, eventName) {\n    Object.defineProperty(\n        eventTargetPrototype,\n        `on${eventName}`,\n        defineEventAttributeDescriptor(eventName)\n    );\n}\n\n/**\n * Define a custom EventTarget with event attributes.\n * @param {string[]} eventNames Event names for event attributes.\n * @returns {EventTarget} The custom EventTarget.\n * @private\n */\nfunction defineCustomEventTarget(eventNames) {\n    /** CustomEventTarget */\n    function CustomEventTarget() {\n        EventTarget.call(this);\n    }\n\n    CustomEventTarget.prototype = Object.create(EventTarget.prototype, {\n        constructor: {\n            value: CustomEventTarget,\n            configurable: true,\n            writable: true,\n        },\n    });\n\n    for (let i = 0; i < eventNames.length; ++i) {\n        defineEventAttribute(CustomEventTarget.prototype, eventNames[i]);\n    }\n\n    return CustomEventTarget\n}\n\n/**\n * EventTarget.\n *\n * - This is constructor if no arguments.\n * - This is a function which returns a CustomEventTarget constructor if there are arguments.\n *\n * For example:\n *\n *     class A extends EventTarget {}\n *     class B extends EventTarget(\"message\") {}\n *     class C extends EventTarget(\"message\", \"error\") {}\n *     class D extends EventTarget([\"message\", \"error\"]) {}\n */\nfunction EventTarget() {\n    /*eslint-disable consistent-return */\n    if (this instanceof EventTarget) {\n        listenersMap.set(this, new Map());\n        return\n    }\n    if (arguments.length === 1 && Array.isArray(arguments[0])) {\n        return defineCustomEventTarget(arguments[0])\n    }\n    if (arguments.length > 0) {\n        const types = new Array(arguments.length);\n        for (let i = 0; i < arguments.length; ++i) {\n            types[i] = arguments[i];\n        }\n        return defineCustomEventTarget(types)\n    }\n    throw new TypeError(\"Cannot call a class as a function\")\n    /*eslint-enable consistent-return */\n}\n\n// Should be enumerable, but class methods are not enumerable.\nEventTarget.prototype = {\n    /**\n     * Add a given listener to this event target.\n     * @param {string} eventName The event name to add.\n     * @param {Function} listener The listener to add.\n     * @param {boolean|{capture?:boolean,passive?:boolean,once?:boolean}} [options] The options for this listener.\n     * @returns {void}\n     */\n    addEventListener(eventName, listener, options) {\n        if (listener == null) {\n            return\n        }\n        if (typeof listener !== \"function\" && !isObject(listener)) {\n            throw new TypeError(\"'listener' should be a function or an object.\")\n        }\n\n        const listeners = getListeners(this);\n        const optionsIsObj = isObject(options);\n        const capture = optionsIsObj\n            ? Boolean(options.capture)\n            : Boolean(options);\n        const listenerType = capture ? CAPTURE : BUBBLE;\n        const newNode = {\n            listener,\n            listenerType,\n            passive: optionsIsObj && Boolean(options.passive),\n            once: optionsIsObj && Boolean(options.once),\n            next: null,\n        };\n\n        // Set it as the first node if the first node is null.\n        let node = listeners.get(eventName);\n        if (node === undefined) {\n            listeners.set(eventName, newNode);\n            return\n        }\n\n        // Traverse to the tail while checking duplication..\n        let prev = null;\n        while (node != null) {\n            if (\n                node.listener === listener &&\n                node.listenerType === listenerType\n            ) {\n                // Should ignore duplication.\n                return\n            }\n            prev = node;\n            node = node.next;\n        }\n\n        // Add it.\n        prev.next = newNode;\n    },\n\n    /**\n     * Remove a given listener from this event target.\n     * @param {string} eventName The event name to remove.\n     * @param {Function} listener The listener to remove.\n     * @param {boolean|{capture?:boolean,passive?:boolean,once?:boolean}} [options] The options for this listener.\n     * @returns {void}\n     */\n    removeEventListener(eventName, listener, options) {\n        if (listener == null) {\n            return\n        }\n\n        const listeners = getListeners(this);\n        const capture = isObject(options)\n            ? Boolean(options.capture)\n            : Boolean(options);\n        const listenerType = capture ? CAPTURE : BUBBLE;\n\n        let prev = null;\n        let node = listeners.get(eventName);\n        while (node != null) {\n            if (\n                node.listener === listener &&\n                node.listenerType === listenerType\n            ) {\n                if (prev !== null) {\n                    prev.next = node.next;\n                } else if (node.next !== null) {\n                    listeners.set(eventName, node.next);\n                } else {\n                    listeners.delete(eventName);\n                }\n                return\n            }\n\n            prev = node;\n            node = node.next;\n        }\n    },\n\n    /**\n     * Dispatch a given event.\n     * @param {Event|{type:string}} event The event to dispatch.\n     * @returns {boolean} `false` if canceled.\n     */\n    dispatchEvent(event) {\n        if (event == null || typeof event.type !== \"string\") {\n            throw new TypeError('\"event.type\" should be a string.')\n        }\n\n        // If listeners aren't registered, terminate.\n        const listeners = getListeners(this);\n        const eventName = event.type;\n        let node = listeners.get(eventName);\n        if (node == null) {\n            return true\n        }\n\n        // Since we cannot rewrite several properties, so wrap object.\n        const wrappedEvent = wrapEvent(this, event);\n\n        // This doesn't process capturing phase and bubbling phase.\n        // This isn't participating in a tree.\n        let prev = null;\n        while (node != null) {\n            // Remove this listener if it's once\n            if (node.once) {\n                if (prev !== null) {\n                    prev.next = node.next;\n                } else if (node.next !== null) {\n                    listeners.set(eventName, node.next);\n                } else {\n                    listeners.delete(eventName);\n                }\n            } else {\n                prev = node;\n            }\n\n            // Call this listener\n            setPassiveListener(\n                wrappedEvent,\n                node.passive ? node.listener : null\n            );\n            if (typeof node.listener === \"function\") {\n                try {\n                    node.listener.call(this, wrappedEvent);\n                } catch (err) {\n                    if (\n                        typeof console !== \"undefined\" &&\n                        typeof console.error === \"function\"\n                    ) {\n                        console.error(err);\n                    }\n                }\n            } else if (\n                node.listenerType !== ATTRIBUTE &&\n                typeof node.listener.handleEvent === \"function\"\n            ) {\n                node.listener.handleEvent(wrappedEvent);\n            }\n\n            // Break if `event.stopImmediatePropagation` was called.\n            if (isStopped(wrappedEvent)) {\n                break\n            }\n\n            node = node.next;\n        }\n        setPassiveListener(wrappedEvent, null);\n        setEventPhase(wrappedEvent, 0);\n        setCurrentTarget(wrappedEvent, null);\n\n        return !wrappedEvent.defaultPrevented\n    },\n};\n\n// `constructor` is not enumerable.\nObject.defineProperty(EventTarget.prototype, \"constructor\", {\n    value: EventTarget,\n    configurable: true,\n    writable: true,\n});\n\n// Ensure `eventTarget instanceof window.EventTarget` is `true`.\nif (\n    typeof window !== \"undefined\" &&\n    typeof window.EventTarget !== \"undefined\"\n) {\n    Object.setPrototypeOf(EventTarget.prototype, window.EventTarget.prototype);\n}\n\nexport default EventTarget;\nexport { defineEventAttribute, EventTarget };\n//# sourceMappingURL=event-target-shim.mjs.map\n", "import {\n    // Event,\n    EventTarget,\n    // Type,\n    defineEventAttribute,\n} from \"event-target-shim\"\n\n// Known Limitation\n//   Use `any` because the type of `AbortSignal` in `lib.dom.d.ts` is wrong and\n//   to make assignable our `AbortSignal` into that.\n//   https://github.com/Microsoft/TSJS-lib-generator/pull/623\ntype Events = {\n    abort: any // Event & Type<\"abort\">\n}\ntype EventAttributes = {\n    onabort: any // Event & Type<\"abort\">\n}\n\n/**\n * The signal class.\n * @see https://dom.spec.whatwg.org/#abortsignal\n */\nexport default class AbortSignal extends EventTarget<Events, EventAttributes> {\n    /**\n     * AbortSignal cannot be constructed directly.\n     */\n    public constructor() {\n        super()\n        throw new TypeError(\"AbortSignal cannot be constructed directly\")\n    }\n\n    /**\n     * Returns `true` if this `AbortSignal`'s `AbortController` has signaled to abort, and `false` otherwise.\n     */\n    public get aborted(): boolean {\n        const aborted = abortedFlags.get(this)\n        if (typeof aborted !== \"boolean\") {\n            throw new TypeError(\n                `Expected 'this' to be an 'AbortSignal' object, but got ${\n                    this === null ? \"null\" : typeof this\n                }`,\n            )\n        }\n        return aborted\n    }\n}\ndefineEventAttribute(AbortSignal.prototype, \"abort\")\n\n/**\n * Create an AbortSignal object.\n */\nexport function createAbortSignal(): AbortSignal {\n    const signal = Object.create(AbortSignal.prototype)\n    EventTarget.call(signal)\n    abortedFlags.set(signal, false)\n    return signal\n}\n\n/**\n * Abort a given signal.\n */\nexport function abortSignal(signal: AbortSignal): void {\n    if (abortedFlags.get(signal) !== false) {\n        return\n    }\n\n    abortedFlags.set(signal, true)\n    signal.dispatchEvent<\"abort\">({ type: \"abort\" })\n}\n\n/**\n * Aborted flag for each instances.\n */\nconst abortedFlags = new WeakMap<AbortSignal, boolean>()\n\n// Properties should be enumerable.\nObject.defineProperties(AbortSignal.prototype, {\n    aborted: { enumerable: true },\n})\n\n// `toString()` should return `\"[object AbortSignal]\"`\nif (typeof Symbol === \"function\" && typeof Symbol.toStringTag === \"symbol\") {\n    Object.defineProperty(AbortSignal.prototype, Symbol.toStringTag, {\n        configurable: true,\n        value: \"AbortSignal\",\n    })\n}\n", "import AbortSignal, { abortSignal, createAbortSignal } from \"./abort-signal\"\n\n/**\n * The AbortController.\n * @see https://dom.spec.whatwg.org/#abortcontroller\n */\nexport default class AbortController {\n    /**\n     * Initialize this controller.\n     */\n    public constructor() {\n        signals.set(this, createAbortSignal())\n    }\n\n    /**\n     * Returns the `AbortSignal` object associated with this object.\n     */\n    public get signal(): AbortSignal {\n        return getSignal(this)\n    }\n\n    /**\n     * Abort and signal to any observers that the associated activity is to be aborted.\n     */\n    public abort(): void {\n        abortSignal(getSignal(this))\n    }\n}\n\n/**\n * Associated signals.\n */\nconst signals = new WeakMap<AbortController, AbortSignal>()\n\n/**\n * Get the associated signal of a given controller.\n */\nfunction getSignal(controller: AbortController): AbortSignal {\n    const signal = signals.get(controller)\n    if (signal == null) {\n        throw new TypeError(\n            `Expected 'this' to be an 'AbortController' object, but got ${\n                controller === null ? \"null\" : typeof controller\n            }`,\n        )\n    }\n    return signal\n}\n\n// Properties should be enumerable.\nObject.defineProperties(AbortController.prototype, {\n    signal: { enumerable: true },\n    abort: { enumerable: true },\n})\n\nif (typeof Symbol === \"function\" && typeof Symbol.toStringTag === \"symbol\") {\n    Object.defineProperty(AbortController.prototype, Symbol.toStringTag, {\n        configurable: true,\n        value: \"AbortController\",\n    })\n}\n\nexport { AbortController, AbortSignal }\n"], "names": ["pd", "event", "retv", "privateData", "get", "console", "assert", "setCancelFlag", "data", "passiveListener", "cancelable", "canceled", "preventDefault", "error", "Event", "eventTarget", "set", "eventPhase", "currentTarget", "stopped", "immediateStopped", "timeStamp", "Date", "now", "Object", "defineProperty", "value", "enumerable", "key", "keys", "i", "length", "defineRedirectDescriptor", "configurable", "defineCallDescriptor", "apply", "arguments", "defineWrapper", "BaseEvent", "proto", "CustomEvent", "call", "prototype", "create", "constructor", "writable", "descriptor", "getOwnPropertyDescriptor", "isFunc", "getWrapper", "wrapper", "wrappers", "getPrototypeOf", "wrapEvent", "Wrapper", "isStopped", "setEventPhase", "set<PERSON><PERSON><PERSON><PERSON>arget", "setPassiveListener", "createAbortSignal", "signal", "AbortSignal", "EventTarget", "abortedFlags", "abortSignal", "dispatchEvent", "type", "getSignal", "controller", "signals", "TypeError", "WeakMap", "target", "<PERSON><PERSON><PERSON>", "NONE", "CAPTURING_PHASE", "AT_TARGET", "BUBBLING_PHASE", "stopPropagation", "stopImmediatePropagation", "bubbles", "defaultPrevented", "composed", "srcElement", "cancelBubble", "returnValue", "initEvent", "window", "setPrototypeOf", "aborted", "defineEventAttribute", "defineProperties", "Symbol", "_typeof", "toStringTag", "AbortController", "abort"], "mappings": ";;;+3CAkCA,QAASA,CAAAA,CAAT,CAAYC,CAAZ,CAAmB,IACTC,CAAAA,CAAI,CAAGC,CAAW,CAACC,GAAZ,CAAgBH,CAAhB,QACbI,CAAAA,OAAO,CAACC,MAAR,CACY,IAAR,EAAAJ,CADJ,CAEI,6CAFJ,CAGID,CAHJ,EAKOC,EAOX,QAASK,CAAAA,CAAT,CAAuBC,CAAvB,CAA6B,OACG,KAAxB,EAAAA,CAAI,CAACC,eADgB,MAarB,CAACD,CAAI,CAACP,KAAL,CAAWS,UAbS,GAiBzBF,CAAI,CAACG,QAAL,GAjByB,CAkBgB,UAArC,QAAOH,CAAAA,CAAI,CAACP,KAAL,CAAWW,cAlBG,EAmBrBJ,CAAI,CAACP,KAAL,CAAWW,cAAX,EAnBqB,QAGE,WAAnB,QAAOP,CAAAA,OAAP,EACyB,UAAzB,QAAOA,CAAAA,OAAO,CAACQ,KAJE,EAMjBR,OAAO,CAACQ,KAAR,CACI,oEADJ,CAEIL,CAAI,CAACC,eAFT,CANiB,EAiC7B,QAASK,CAAAA,CAAT,CAAeC,CAAf,CAA4Bd,CAA5B,CAAmC,CAC/BE,CAAW,CAACa,GAAZ,CAAgB,IAAhB,CAAsB,CAClBD,WAAW,CAAXA,CADkB,CAElBd,KAAK,CAALA,CAFkB,CAGlBgB,UAAU,CAAE,CAHM,CAIlBC,aAAa,CAAEH,CAJG,CAKlBJ,QAAQ,GALU,CAMlBQ,OAAO,GANW,CAOlBC,gBAAgB,GAPE,CAQlBX,eAAe,CAAE,IARC,CASlBY,SAAS,CAAEpB,CAAK,CAACoB,SAAN,EAAmBC,IAAI,CAACC,GAAL,EATZ,CAAtB,CAD+B,CAc/BC,MAAM,CAACC,cAAP,CAAsB,IAAtB,CAA4B,WAA5B,CAAyC,CAAEC,KAAK,GAAP,CAAgBC,UAAU,GAA1B,CAAzC,CAd+B,QAmBrBC,CAAAA,EAFJC,CAAI,CAAGL,MAAM,CAACK,IAAP,CAAY5B,CAAZ,EACJ6B,CAAC,CAAG,EAAGA,CAAC,CAAGD,CAAI,CAACE,OAAQ,EAAED,EACzBF,EAAMC,CAAI,CAACC,CAAD,EACVF,CAAG,GAAI,OACTJ,MAAM,CAACC,cAAP,CAAsB,IAAtB,CAA4BG,CAA5B,CAAiCI,CAAwB,CAACJ,CAAD,CAAzD,EAyOZ,QAASI,CAAAA,CAAT,CAAkCJ,CAAlC,CAAuC,OAC5B,CACHxB,GADG,WACG,OACKJ,CAAAA,CAAE,CAAC,IAAD,CAAF,CAASC,KAAT,CAAe2B,CAAf,CAFR,CAAA,CAIHZ,GAJG,UAICU,EAAO,CACP1B,CAAE,CAAC,IAAD,CAAF,CAASC,KAAT,CAAe2B,CAAf,EAAsBF,CALvB,CAAA,CAOHO,YAAY,GAPT,CAQHN,UAAU,GARP,EAkBX,QAASO,CAAAA,CAAT,CAA8BN,CAA9B,CAAmC,OACxB,CACHF,KADG,WACK,IACEzB,CAAAA,CAAK,CAAGD,CAAE,CAAC,IAAD,CAAF,CAASC,YAChBA,CAAAA,CAAK,CAAC2B,CAAD,CAAL,CAAWO,KAAX,CAAiBlC,CAAjB,CAAwBmC,SAAxB,CAHR,CAAA,CAKHH,YAAY,GALT,CAMHN,UAAU,GANP,EAiBX,QAASU,CAAAA,CAAT,CAAuBC,CAAvB,CAAkCC,CAAlC,CAAyC,SAO5BC,CAAAA,EAAYzB,EAAad,EAAO,CACrCqC,CAAS,CAACG,IAAV,CAAe,IAAf,CAAqB1B,CAArB,CAAkCd,CAAlC,KAPE4B,CAAAA,CAAI,CAAGL,MAAM,CAACK,IAAP,CAAYU,CAAZ,KACO,CAAhB,GAAAV,CAAI,CAACE,aACEO,CAAAA,EAQXE,CAAW,CAACE,SAAZ,CAAwBlB,MAAM,CAACmB,MAAP,CAAcL,CAAS,CAACI,SAAxB,CAAmC,CACvDE,WAAW,CAAE,CAAElB,KAAK,CAAEc,CAAT,CAAsBP,YAAY,GAAlC,CAA0CY,QAAQ,GAAlD,CAD0C,CAAnC,CAXa,KAgBhC,GACKjB,CAAAA,CADL,CAAIE,CAAC,CAAG,EAAGA,CAAC,CAAGD,CAAI,CAACE,OAAQ,EAAED,KACzBF,EAAMC,CAAI,CAACC,CAAD,EACZ,EAAEF,CAAG,GAAIU,CAAAA,CAAS,CAACI,SAAnB,EAA+B,IACzBI,CAAAA,CAAU,CAAGtB,MAAM,CAACuB,wBAAP,CAAgCR,CAAhC,CAAuCX,CAAvC,CADY,CAEzBoB,CAAM,CAA+B,UAA5B,QAAOF,CAAAA,CAAU,CAACpB,KAFF,CAG/BF,MAAM,CAACC,cAAP,CACIe,CAAW,CAACE,SADhB,CAEId,CAFJ,CAGIoB,CAAM,CACAd,CAAoB,CAACN,CAAD,CADpB,CAEAI,CAAwB,CAACJ,CAAD,CALlC,QAUDY,CAAAA,EASX,QAASS,CAAAA,CAAT,CAAoBV,CAApB,CAA2B,IACV,IAAT,EAAAA,CAAK,EAAYA,CAAK,GAAKf,MAAM,CAACkB,gBAC3B5B,CAAAA,KAGPoC,CAAAA,CAAO,CAAGC,CAAQ,CAAC/C,GAAT,CAAamC,CAAb,QACC,KAAX,EAAAW,IACAA,CAAO,CAAGb,CAAa,CAACY,CAAU,CAACzB,MAAM,CAAC4B,cAAP,CAAsBb,CAAtB,CAAD,CAAX,CAA2CA,CAA3C,EACvBY,CAAQ,CAACnC,GAAT,CAAauB,CAAb,CAAoBW,CAApB,GAEGA,EAUJ,QAASG,CAAAA,CAAT,CAAmBtC,CAAnB,CAAgCd,CAAhC,CAAuC,IACpCqD,CAAAA,CAAO,CAAGL,CAAU,CAACzB,MAAM,CAAC4B,cAAP,CAAsBnD,CAAtB,CAAD,QACnB,IAAIqD,CAAAA,CAAJ,CAAYvC,CAAZ,CAAyBd,CAAzB,EASJ,QAASsD,CAAAA,CAAT,CAAmBtD,CAAnB,CAA0B,OACtBD,CAAAA,CAAE,CAACC,CAAD,CAAF,CAAUmB,iBAUd,QAASoC,CAAAA,CAAT,CAAuBvD,CAAvB,CAA8BgB,CAA9B,CAA0C,CAC7CjB,CAAE,CAACC,CAAD,CAAF,CAAUgB,UAAV,CAAuBA,EAUpB,QAASwC,CAAAA,CAAT,CAA0BxD,CAA1B,CAAiCiB,CAAjC,CAAgD,CACnDlB,CAAE,CAACC,CAAD,CAAF,CAAUiB,aAAV,CAA0BA,EAUvB,QAASwC,CAAAA,CAAT,CAA4BzD,CAA5B,CAAmCQ,CAAnC,CAAoD,CACvDT,CAAE,CAACC,CAAD,CAAF,CAAUQ,eAAV,CAA4BA,ysCC1ahBkD,CAAAA,OACNC,CAAAA,CAAM,CAAGpC,MAAM,CAACmB,MAAPnB,CAAcqC,CAAW,CAACnB,SAA1BlB,QACfsC,CAAAA,CAAW,CAACrB,IAAZqB,CAAiBF,CAAjBE,EACAC,CAAY,CAAC/C,GAAb+C,CAAiBH,CAAjBG,KACOH,UAMKI,CAAAA,EAAYJ,GACpBG,KAAAA,CAAY,CAAC3D,GAAb2D,CAAiBH,CAAjBG,IAIJA,CAAY,CAAC/C,GAAb+C,CAAiBH,CAAjBG,KACAH,CAAM,CAACK,aAAPL,CAA8B,CAAEM,IAAI,CAAE,OAAR,CAA9BN,GC9BJ,QAASO,CAAAA,CAAT,CAAmBC,CAAnB,KACUR,CAAAA,CAAM,CAAGS,CAAO,CAACjE,GAARiE,CAAYD,CAAZC,KACD,IAAVT,EAAAA,OACM,IAAIU,CAAAA,SAAJ,sEAEiB,IAAfF,GAAAA,CAAU,CAAY,MAAZ,GAA4BA,GAFxC,QAMHR,CAAAA,KF3BLzD,CAAAA,CAAW,CAAG,GAAIoE,CAAAA,QAOlBpB,CAAQ,CAAG,GAAIoB,CAAAA,QAkFrBzD,CAAK,CAAC4B,SAAN,CAAkB,IAKVwB,CAAAA,MAAO,OACAlE,CAAAA,CAAE,CAAC,IAAD,CAAF,CAASC,KAAT,CAAeiE,IANZ,CAAA,IAaVM,CAAAA,QAAS,OACFxE,CAAAA,CAAE,CAAC,IAAD,CAAF,CAASe,WAdN,CAAA,IAqBVG,CAAAA,eAAgB,OACTlB,CAAAA,CAAE,CAAC,IAAD,CAAF,CAASkB,aAtBN,CAAA,CA4BduD,YA5Bc,WA4BC,IACLvD,CAAAA,CAAa,CAAGlB,CAAE,CAAC,IAAD,CAAF,CAASkB,cADpB,MAEU,KAAjB,EAAAA,CAFO,CAGA,EAHA,CAKJ,CAACA,CAAD,CAjCG,CAAA,IAwCVwD,CAAAA,MAAO,OACA,EAzCG,CAAA,IAgDVC,CAAAA,iBAAkB,OACX,EAjDG,CAAA,IAwDVC,CAAAA,WAAY,OACL,EAzDG,CAAA,IAgEVC,CAAAA,gBAAiB,OACV,EAjEG,CAAA,IAwEV5D,CAAAA,YAAa,OACNjB,CAAAA,CAAE,CAAC,IAAD,CAAF,CAASiB,UAzEN,CAAA,CAgFd6D,eAhFc,WAgFI,IACRtE,CAAAA,CAAI,CAAGR,CAAE,CAAC,IAAD,EAEfQ,CAAI,CAACW,OAAL,GAHc,CAI4B,UAAtC,QAAOX,CAAAA,CAAI,CAACP,KAAL,CAAW6E,eAJR,EAKVtE,CAAI,CAACP,KAAL,CAAW6E,eAAX,EArFM,CAAA,CA6FdC,wBA7Fc,WA6Fa,IACjBvE,CAAAA,CAAI,CAAGR,CAAE,CAAC,IAAD,EAEfQ,CAAI,CAACW,OAAL,GAHuB,CAIvBX,CAAI,CAACY,gBAAL,GAJuB,CAK4B,UAA/C,QAAOZ,CAAAA,CAAI,CAACP,KAAL,CAAW8E,wBALC,EAMnBvE,CAAI,CAACP,KAAL,CAAW8E,wBAAX,EAnGM,CAAA,IA2GVC,CAAAA,SAAU,SACKhF,CAAE,CAAC,IAAD,CAAF,CAASC,KAAT,CAAe+E,OA5GpB,CAAA,IAmHVtE,CAAAA,YAAa,SACEV,CAAE,CAAC,IAAD,CAAF,CAASC,KAAT,CAAeS,UApHpB,CAAA,CA2HdE,cA3Hc,WA2HG,CACbL,CAAa,CAACP,CAAE,CAAC,IAAD,CAAH,CA5HH,CAAA,IAmIViF,CAAAA,kBAAmB,OACZjF,CAAAA,CAAE,CAAC,IAAD,CAAF,CAASW,QApIN,CAAA,IA2IVuE,CAAAA,UAAW,SACIlF,CAAE,CAAC,IAAD,CAAF,CAASC,KAAT,CAAeiF,QA5IpB,CAAA,IAmJV7D,CAAAA,WAAY,OACLrB,CAAAA,CAAE,CAAC,IAAD,CAAF,CAASqB,SApJN,CAAA,IA4JV8D,CAAAA,YAAa,OACNnF,CAAAA,CAAE,CAAC,IAAD,CAAF,CAASe,WA7JN,CAAA,IAqKVqE,CAAAA,cAAe,OACRpF,CAAAA,CAAE,CAAC,IAAD,CAAF,CAASmB,OAtKN,CAAA,IAwKViE,CAAAA,aAAa1D,EAAO,IACfA,MAGClB,CAAAA,CAAI,CAAGR,CAAE,CAAC,IAAD,EAEfQ,CAAI,CAACW,OAAL,IACuC,SAAnC,QAAOX,CAAAA,CAAI,CAACP,KAAL,CAAWmF,eAClB5E,CAAI,CAACP,KAAL,CAAWmF,YAAX,KAhLM,CAAA,IAyLVC,CAAAA,aAAc,OACP,CAACrF,CAAE,CAAC,IAAD,CAAF,CAASW,QA1LP,CAAA,IA4LV0E,CAAAA,YAAY3D,EAAO,CACdA,CADc,EAEfnB,CAAa,CAACP,CAAE,CAAC,IAAD,CAAH,CA9LP,CAAA,CAyMdsF,SAzMc,WAyMF,EAzME,EA+MlB9D,MAAM,CAACC,cAAP,CAAsBX,CAAK,CAAC4B,SAA5B,CAAuC,aAAvC,CAAsD,CAClDhB,KAAK,CAAEZ,CAD2C,CAElDmB,YAAY,GAFsC,CAGlDY,QAAQ,GAH0C,CAAtD,EAOsB,WAAlB,QAAO0C,CAAAA,MAAP,EAAyD,WAAxB,QAAOA,CAAAA,MAAM,CAACzE,QAC/CU,MAAM,CAACgE,cAAP,CAAsB1E,CAAK,CAAC4B,SAA5B,CAAuC6C,MAAM,CAACzE,KAAP,CAAa4B,SAApD,EAGAS,CAAQ,CAACnC,GAAT,CAAauE,MAAM,CAACzE,KAAP,CAAa4B,SAA1B,CAAqC5B,CAArC,wiDChTiB+C,CAAAA,2EAMP,GAAIS,CAAAA,SAAJ,CAAc,4CAAd,sDAOAmB,CAAAA,CAAO,CAAG1B,CAAY,CAAC3D,GAAb2D,CAAiB,IAAjBA,KACO,SAAnB,QAAO0B,CAAAA,OACD,IAAInB,CAAAA,SAAJ,kEAEW,IAAT,QAAgB,MAAhB,GAAgC,MAFlC,QAMHmB,CAAAA,SArB0B3B,GAwBzC4B,CAAoB,CAAC7B,CAAW,CAACnB,SAAb,CAAwB,OAAxB,EA2BpB,GAAMqB,CAAAA,CAAY,CAAG,GAAIQ,CAAAA,OAAzB,CAGA/C,MAAM,CAACmE,gBAAPnE,CAAwBqC,CAAW,CAACnB,SAApClB,CAA+C,CAC3CiE,OAAO,CAAE,CAAE9D,UAAU,GAAZ,CADkC,CAA/CH,EAKsB,UAAlB,QAAOoE,CAAAA,MAAP,EAA8D,QAA9B,GAAAC,EAAOD,MAAM,CAACE,cAC9CtE,MAAM,CAACC,cAAPD,CAAsBqC,CAAW,CAACnB,SAAlClB,CAA6CoE,MAAM,CAACE,WAApDtE,CAAiE,CAC7DS,YAAY,GADiD,CAE7DP,KAAK,CAAE,aAFsD,CAAjEF,KC5EiBuE,CAAAA,oCAKb1B,CAAO,CAACrD,GAARqD,CAAY,IAAZA,CAAkBV,CAAiB,EAAnCU,4CAcAL,CAAW,CAACG,CAAS,CAAC,IAAD,CAAV,uCAPJA,CAAAA,CAAS,CAAC,IAAD,WAclBE,CAAO,CAAG,GAAIE,CAAAA,WAkBpB/C,MAAM,CAACmE,gBAAPnE,CAAwBuE,CAAe,CAACrD,SAAxClB,CAAmD,CAC/CoC,MAAM,CAAE,CAAEjC,UAAU,GAAZ,CADuC,CAE/CqE,KAAK,CAAE,CAAErE,UAAU,GAAZ,CAFwC,CAAnDH,EAKsB,UAAlB,QAAOoE,CAAAA,MAAP,EAA8D,QAA9B,GAAAC,EAAOD,MAAM,CAACE,cAC9CtE,MAAM,CAACC,cAAPD,CAAsBuE,CAAe,CAACrD,SAAtClB,CAAiDoE,MAAM,CAACE,WAAxDtE,CAAqE,CACjES,YAAY,GADqD,CAEjEP,KAAK,CAAE,iBAF0D,CAArEF"}