{"predef": [], "bitwise": false, "camelcase": false, "curly": false, "eqeqeq": false, "forin": false, "immed": false, "latedef": false, "noarg": true, "noempty": true, "nonew": true, "plusplus": false, "quotmark": true, "regexp": false, "undef": true, "unused": true, "strict": false, "trailing": true, "maxlen": 120, "asi": true, "boss": true, "debug": true, "eqnull": true, "esnext": true, "evil": true, "expr": true, "funcscope": false, "globalstrict": false, "iterator": false, "lastsemic": true, "laxbreak": true, "laxcomma": true, "loopfunc": true, "multistr": false, "onecase": false, "proto": false, "regexdash": false, "scripturl": true, "smarttabs": false, "shadow": false, "sub": true, "supernew": false, "validthis": true, "browser": true, "couch": false, "devel": false, "dojo": false, "mootools": false, "node": true, "nonstandard": true, "prototypejs": false, "rhino": false, "worker": true, "wsh": false, "nomen": false, "onevar": false, "passfail": false}