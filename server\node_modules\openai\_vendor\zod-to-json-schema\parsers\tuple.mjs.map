{"version": 3, "file": "tuple.mjs", "sourceRoot": "", "sources": ["../../../src/_vendor/zod-to-json-schema/parsers/tuple.ts"], "names": [], "mappings": "OACO,EAAmB,QAAQ,EAAE;AAgBpC,MAAM,UAAU,aAAa,CAC3B,GAAuD,EACvD,IAAU;IAEV,IAAI,GAAG,CAAC,IAAI,EAAE;QACZ,OAAO;YACL,IAAI,EAAE,OAAO;YACb,QAAQ,EAAE,GAAG,CAAC,KAAK,CAAC,MAAM;YAC1B,KAAK,EAAE,GAAG,CAAC,KAAK;iBACb,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACZ,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE;gBACf,GAAG,IAAI;gBACP,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,GAAG,CAAC,EAAE,CAAC;aACpD,CAAC,CACH;iBACA,MAAM,CAAC,CAAC,GAAsB,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YACnF,eAAe,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;gBACvC,GAAG,IAAI;gBACP,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,iBAAiB,CAAC;aACtD,CAAC;SACH,CAAC;KACH;SAAM;QACL,OAAO;YACL,IAAI,EAAE,OAAO;YACb,QAAQ,EAAE,GAAG,CAAC,KAAK,CAAC,MAAM;YAC1B,QAAQ,EAAE,GAAG,CAAC,KAAK,CAAC,MAAM;YAC1B,KAAK,EAAE,GAAG,CAAC,KAAK;iBACb,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACZ,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE;gBACf,GAAG,IAAI;gBACP,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,GAAG,CAAC,EAAE,CAAC;aACpD,CAAC,CACH;iBACA,MAAM,CAAC,CAAC,GAAsB,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;SACpF,CAAC;KACH;AACH,CAAC"}