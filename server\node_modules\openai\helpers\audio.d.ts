                              
import { File } from 'formdata-node';
import { Response } from 'openai/_shims';
export declare function playAudio(input: NodeJS.ReadableStream | Response | File): Promise<void>;
type RecordAudioOptions = {
    signal?: AbortSignal;
    device?: number;
    timeout?: number;
};
export declare function recordAudio(options?: RecordAudioOptions): Promise<File>;
export {};
//# sourceMappingURL=audio.d.ts.map