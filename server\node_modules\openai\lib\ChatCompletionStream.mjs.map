{"version": 3, "file": "ChatCompletionStream.mjs", "sourceRoot": "", "sources": ["../src/lib/ChatCompletionStream.ts"], "names": [], "mappings": ";;;;;;;;;;;;OACO,EACL,WAAW,EACX,iBAAiB,EACjB,uBAAuB,EACvB,8BAA8B,GAC/B;OAUM,EACL,4BAA4B,GAE7B;OAEM,EAAE,MAAM,EAAE;OAGV,EAEL,qBAAqB,EACrB,4BAA4B,EAC5B,kBAAkB,EAClB,wBAAwB,EACxB,mBAAmB,GACpB;OACM,EAAE,YAAY,EAAE;AA+FvB,MAAM,OAAO,oBACX,SAAQ,4BAA0E;IAOlF,YAAY,MAAyC;QACnD,KAAK,EAAE,CAAC;;QALV,+CAA2C;QAC3C,0DAAuC;QACvC,sEAAmE;QAIjE,uBAAA,IAAI,gCAAW,MAAM,MAAA,CAAC;QACtB,uBAAA,IAAI,2CAAsB,EAAE,MAAA,CAAC;IAC/B,CAAC;IAED,IAAI,6BAA6B;QAC/B,OAAO,uBAAA,IAAI,2DAA+B,CAAC;IAC7C,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,kBAAkB,CAAC,MAAsB;QAC9C,MAAM,MAAM,GAAG,IAAI,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAC9C,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC;QACtD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,oBAAoB,CACzB,MAAc,EACd,MAAkC,EAClC,OAA6B;QAE7B,MAAM,MAAM,GAAG,IAAI,oBAAoB,CAAU,MAA6C,CAAC,CAAC;QAChG,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CACf,MAAM,CAAC,kBAAkB,CACvB,MAAM,EACN,EAAE,GAAG,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAC3B,EAAE,GAAG,OAAO,EAAE,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,OAAO,EAAE,2BAA2B,EAAE,QAAQ,EAAE,EAAE,CACxF,CACF,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;IAoMkB,KAAK,CAAC,qBAAqB,CAC5C,MAAc,EACd,MAAkC,EAClC,OAA6B;QAE7B,KAAK,CAAC,qBAAqB,CAAC;QAC5B,MAAM,MAAM,GAAG,OAAO,EAAE,MAAM,CAAC;QAC/B,IAAI,MAAM,EAAE;YACV,IAAI,MAAM,CAAC,OAAO;gBAAE,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YAC5C,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC;SACjE;QACD,uBAAA,IAAI,2EAAc,MAAlB,IAAI,CAAgB,CAAC;QAErB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CACjD,EAAE,GAAG,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAC3B,EAAE,GAAG,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAC/C,CAAC;QACF,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE;YAChC,uBAAA,IAAI,uEAAU,MAAd,IAAI,EAAW,KAAK,CAAC,CAAC;SACvB;QACD,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE;YACrC,MAAM,IAAI,iBAAiB,EAAE,CAAC;SAC/B;QACD,OAAO,IAAI,CAAC,kBAAkB,CAAC,uBAAA,IAAI,yEAAY,MAAhB,IAAI,CAAc,CAAC,CAAC;IACrD,CAAC;IAES,KAAK,CAAC,mBAAmB,CACjC,cAA8B,EAC9B,OAA6B;QAE7B,MAAM,MAAM,GAAG,OAAO,EAAE,MAAM,CAAC;QAC/B,IAAI,MAAM,EAAE;YACV,IAAI,MAAM,CAAC,OAAO;gBAAE,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YAC5C,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC;SACjE;QACD,uBAAA,IAAI,2EAAc,MAAlB,IAAI,CAAgB,CAAC;QACrB,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,MAAM,MAAM,GAAG,MAAM,CAAC,kBAAkB,CAAsB,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/F,IAAI,MAAM,CAAC;QACX,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE;YAChC,IAAI,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC,EAAE,EAAE;gBACjC,+BAA+B;gBAC/B,IAAI,CAAC,kBAAkB,CAAC,uBAAA,IAAI,yEAAY,MAAhB,IAAI,CAAc,CAAC,CAAC;aAC7C;YAED,uBAAA,IAAI,uEAAU,MAAd,IAAI,EAAW,KAAK,CAAC,CAAC;YACtB,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC;SACnB;QACD,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE;YACrC,MAAM,IAAI,iBAAiB,EAAE,CAAC;SAC/B;QACD,OAAO,IAAI,CAAC,kBAAkB,CAAC,uBAAA,IAAI,yEAAY,MAAhB,IAAI,CAAc,CAAC,CAAC;IACrD,CAAC;IAuHD;QA7WE,IAAI,IAAI,CAAC,KAAK;YAAE,OAAO;QACvB,uBAAA,IAAI,uDAAkC,SAAS,MAAA,CAAC;IAClD,CAAC,iGAEoB,MAAqC;QACxD,IAAI,KAAK,GAAG,uBAAA,IAAI,+CAAmB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAClD,IAAI,KAAK,EAAE;YACT,OAAO,KAAK,CAAC;SACd;QAED,KAAK,GAAG;YACN,YAAY,EAAE,KAAK;YACnB,YAAY,EAAE,KAAK;YACnB,qBAAqB,EAAE,KAAK;YAC5B,qBAAqB,EAAE,KAAK;YAC5B,eAAe,EAAE,IAAI,GAAG,EAAE;YAC1B,uBAAuB,EAAE,IAAI;SAC9B,CAAC;QACF,uBAAA,IAAI,+CAAmB,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;QAC9C,OAAO,KAAK,CAAC;IACf,CAAC,2EAE8C,KAA0B;QACvE,IAAI,IAAI,CAAC,KAAK;YAAE,OAAO;QAEvB,MAAM,UAAU,GAAG,uBAAA,IAAI,uFAA0B,MAA9B,IAAI,EAA2B,KAAK,CAAC,CAAC;QACzD,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;QAEvC,KAAK,MAAM,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE;YAClC,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAE,CAAC;YAEzD,IACE,MAAM,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI;gBAC5B,cAAc,CAAC,OAAO,EAAE,IAAI,KAAK,WAAW;gBAC5C,cAAc,CAAC,OAAO,EAAE,OAAO,EAC/B;gBACA,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAC5E,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;oBAC1B,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO;oBAC3B,QAAQ,EAAE,cAAc,CAAC,OAAO,CAAC,OAAO;oBACxC,MAAM,EAAE,cAAc,CAAC,OAAO,CAAC,MAAM;iBACtC,CAAC,CAAC;aACJ;YAED,IACE,MAAM,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI;gBAC5B,cAAc,CAAC,OAAO,EAAE,IAAI,KAAK,WAAW;gBAC5C,cAAc,CAAC,OAAO,EAAE,OAAO,EAC/B;gBACA,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;oBAC1B,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO;oBAC3B,QAAQ,EAAE,cAAc,CAAC,OAAO,CAAC,OAAO;iBACzC,CAAC,CAAC;aACJ;YAED,IAAI,MAAM,CAAC,QAAQ,EAAE,OAAO,IAAI,IAAI,IAAI,cAAc,CAAC,OAAO,EAAE,IAAI,KAAK,WAAW,EAAE;gBACpF,IAAI,CAAC,KAAK,CAAC,wBAAwB,EAAE;oBACnC,OAAO,EAAE,MAAM,CAAC,QAAQ,EAAE,OAAO;oBACjC,QAAQ,EAAE,cAAc,CAAC,QAAQ,EAAE,OAAO,IAAI,EAAE;iBACjD,CAAC,CAAC;aACJ;YAED,IAAI,MAAM,CAAC,QAAQ,EAAE,OAAO,IAAI,IAAI,IAAI,cAAc,CAAC,OAAO,EAAE,IAAI,KAAK,WAAW,EAAE;gBACpF,IAAI,CAAC,KAAK,CAAC,wBAAwB,EAAE;oBACnC,OAAO,EAAE,MAAM,CAAC,QAAQ,EAAE,OAAO;oBACjC,QAAQ,EAAE,cAAc,CAAC,QAAQ,EAAE,OAAO,IAAI,EAAE;iBACjD,CAAC,CAAC;aACJ;YAED,MAAM,KAAK,GAAG,uBAAA,IAAI,kFAAqB,MAAzB,IAAI,EAAsB,cAAc,CAAC,CAAC;YAExD,IAAI,cAAc,CAAC,aAAa,EAAE;gBAChC,uBAAA,IAAI,oFAAuB,MAA3B,IAAI,EAAwB,cAAc,CAAC,CAAC;gBAE5C,IAAI,KAAK,CAAC,uBAAuB,IAAI,IAAI,EAAE;oBACzC,uBAAA,IAAI,oFAAuB,MAA3B,IAAI,EAAwB,cAAc,EAAE,KAAK,CAAC,uBAAuB,CAAC,CAAC;iBAC5E;aACF;YAED,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,IAAI,EAAE,EAAE;gBACpD,IAAI,KAAK,CAAC,uBAAuB,KAAK,QAAQ,CAAC,KAAK,EAAE;oBACpD,uBAAA,IAAI,oFAAuB,MAA3B,IAAI,EAAwB,cAAc,CAAC,CAAC;oBAE5C,kDAAkD;oBAClD,IAAI,KAAK,CAAC,uBAAuB,IAAI,IAAI,EAAE;wBACzC,uBAAA,IAAI,oFAAuB,MAA3B,IAAI,EAAwB,cAAc,EAAE,KAAK,CAAC,uBAAuB,CAAC,CAAC;qBAC5E;iBACF;gBAED,KAAK,CAAC,uBAAuB,GAAG,QAAQ,CAAC,KAAK,CAAC;aAChD;YAED,KAAK,MAAM,aAAa,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,IAAI,EAAE,EAAE;gBACzD,MAAM,gBAAgB,GAAG,cAAc,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAClF,IAAI,CAAC,gBAAgB,EAAE,IAAI,EAAE;oBAC3B,SAAS;iBACV;gBAED,IAAI,gBAAgB,EAAE,IAAI,KAAK,UAAU,EAAE;oBACzC,IAAI,CAAC,KAAK,CAAC,qCAAqC,EAAE;wBAChD,IAAI,EAAE,gBAAgB,CAAC,QAAQ,EAAE,IAAI;wBACrC,KAAK,EAAE,aAAa,CAAC,KAAK;wBAC1B,SAAS,EAAE,gBAAgB,CAAC,QAAQ,CAAC,SAAS;wBAC9C,gBAAgB,EAAE,gBAAgB,CAAC,QAAQ,CAAC,gBAAgB;wBAC5D,eAAe,EAAE,aAAa,CAAC,QAAQ,EAAE,SAAS,IAAI,EAAE;qBACzD,CAAC,CAAC;iBACJ;qBAAM;oBACL,WAAW,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;iBACrC;aACF;SACF;IACH,CAAC,qGAEsB,cAA6C,EAAE,aAAqB;QACzF,MAAM,KAAK,GAAG,uBAAA,IAAI,kFAAqB,MAAzB,IAAI,EAAsB,cAAc,CAAC,CAAC;QACxD,IAAI,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;YAC5C,qCAAqC;YACrC,OAAO;SACR;QAED,MAAM,gBAAgB,GAAG,cAAc,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,aAAa,CAAC,CAAC;QAC5E,IAAI,CAAC,gBAAgB,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SAC1C;QACD,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;SACtD;QAED,IAAI,gBAAgB,CAAC,IAAI,KAAK,UAAU,EAAE;YACxC,MAAM,SAAS,GAAG,uBAAA,IAAI,oCAAQ,EAAE,KAAK,EAAE,IAAI,CACzC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAC5F,CAAC;YAEF,IAAI,CAAC,KAAK,CAAC,oCAAoC,EAAE;gBAC/C,IAAI,EAAE,gBAAgB,CAAC,QAAQ,CAAC,IAAI;gBACpC,KAAK,EAAE,aAAa;gBACpB,SAAS,EAAE,gBAAgB,CAAC,QAAQ,CAAC,SAAS;gBAC9C,gBAAgB,EACd,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,SAAS,CAAC;oBACxF,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,QAAQ,CAAC,SAAS,CAAC;wBAC9E,CAAC,CAAC,IAAI;aACT,CAAC,CAAC;SACJ;aAAM;YACL,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;SACpC;IACH,CAAC,qGAEsB,cAA6C;QAClE,MAAM,KAAK,GAAG,uBAAA,IAAI,kFAAqB,MAAzB,IAAI,EAAsB,cAAc,CAAC,CAAC;QAExD,IAAI,cAAc,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;YACzD,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;YAE1B,MAAM,cAAc,GAAG,uBAAA,IAAI,6FAAgC,MAApC,IAAI,CAAkC,CAAC;YAE9D,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;gBACzB,OAAO,EAAE,cAAc,CAAC,OAAO,CAAC,OAAO;gBACvC,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAE,IAAY;aAClG,CAAC,CAAC;SACJ;QAED,IAAI,cAAc,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;YACzD,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;YAE1B,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,cAAc,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;SACzE;QAED,IAAI,cAAc,CAAC,QAAQ,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE;YACpE,KAAK,CAAC,qBAAqB,GAAG,IAAI,CAAC;YAEnC,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,OAAO,EAAE,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;SACnF;QAED,IAAI,cAAc,CAAC,QAAQ,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE;YACpE,KAAK,CAAC,qBAAqB,GAAG,IAAI,CAAC;YAEnC,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,OAAO,EAAE,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;SACnF;IACH,CAAC;QAGC,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,MAAM,IAAI,WAAW,CAAC,yCAAyC,CAAC,CAAC;SAClE;QACD,MAAM,QAAQ,GAAG,uBAAA,IAAI,2DAA+B,CAAC;QACrD,IAAI,CAAC,QAAQ,EAAE;YACb,MAAM,IAAI,WAAW,CAAC,0CAA0C,CAAC,CAAC;SACnE;QACD,uBAAA,IAAI,uDAAkC,SAAS,MAAA,CAAC;QAChD,uBAAA,IAAI,2CAAsB,EAAE,MAAA,CAAC;QAC7B,OAAO,sBAAsB,CAAC,QAAQ,EAAE,uBAAA,IAAI,oCAAQ,CAAC,CAAC;IACxD,CAAC;QA0DC,MAAM,cAAc,GAAG,uBAAA,IAAI,oCAAQ,EAAE,eAAe,CAAC;QACrD,IAAI,4BAA4B,CAAU,cAAc,CAAC,EAAE;YACzD,OAAO,cAAc,CAAC;SACvB;QAED,OAAO,IAAI,CAAC;IACd,CAAC,2GAEyB,KAA0B;;QAClD,IAAI,QAAQ,GAAG,uBAAA,IAAI,2DAA+B,CAAC;QACnD,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,GAAG,KAAK,CAAC;QACnC,IAAI,CAAC,QAAQ,EAAE;YACb,QAAQ,GAAG,uBAAA,IAAI,uDAAkC;gBAC/C,GAAG,IAAI;gBACP,OAAO,EAAE,EAAE;aACZ,MAAA,CAAC;SACH;aAAM;YACL,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;SAC/B;QAED,KAAK,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,KAAK,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;YACtF,IAAI,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACrC,IAAI,CAAC,MAAM,EAAE;gBACX,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,KAAK,EAAE,CAAC;aAC9F;YAED,IAAI,QAAQ,EAAE;gBACZ,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;oBACpB,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;iBAC/C;qBAAM;oBACL,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,GAAG,QAAQ,CAAC;oBAC/C,aAAa,CAAC,IAAI,CAAC,CAAC;oBACpB,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;oBAErC,IAAI,OAAO,EAAE;wBACX,MAAA,MAAM,CAAC,QAAQ,EAAC,OAAO,QAAP,OAAO,GAAK,EAAE,EAAC;wBAC/B,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;qBAC1C;oBAED,IAAI,OAAO,EAAE;wBACX,MAAA,MAAM,CAAC,QAAQ,EAAC,OAAO,QAAP,OAAO,GAAK,EAAE,EAAC;wBAC/B,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;qBAC1C;iBACF;aACF;YAED,IAAI,aAAa,EAAE;gBACjB,MAAM,CAAC,aAAa,GAAG,aAAa,CAAC;gBAErC,IAAI,uBAAA,IAAI,oCAAQ,IAAI,qBAAqB,CAAC,uBAAA,IAAI,oCAAQ,CAAC,EAAE;oBACvD,IAAI,aAAa,KAAK,QAAQ,EAAE;wBAC9B,MAAM,IAAI,uBAAuB,EAAE,CAAC;qBACrC;oBAED,IAAI,aAAa,KAAK,gBAAgB,EAAE;wBACtC,MAAM,IAAI,8BAA8B,EAAE,CAAC;qBAC5C;iBACF;aACF;YAED,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YAE7B,IAAI,CAAC,KAAK;gBAAE,SAAS,CAAC,kCAAkC;YAExD,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,IAAI,EAAE,GAAG,KAAK,CAAC;YAC7E,aAAa,CAAC,IAAI,CAAC,CAAC;YACpB,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAEpC,IAAI,OAAO,EAAE;gBACX,MAAM,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC,GAAG,OAAO,CAAC;aACnE;YAED,IAAI,IAAI;gBAAE,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;YACrC,IAAI,aAAa,EAAE;gBACjB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,EAAE;oBACjC,MAAM,CAAC,OAAO,CAAC,aAAa,GAAG,aAAa,CAAC;iBAC9C;qBAAM;oBACL,IAAI,aAAa,CAAC,IAAI;wBAAE,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC;oBAC/E,IAAI,aAAa,CAAC,SAAS,EAAE;wBAC3B,MAAA,MAAM,CAAC,OAAO,CAAC,aAAa,EAAC,SAAS,QAAT,SAAS,GAAK,EAAE,EAAC;wBAC9C,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,IAAI,aAAa,CAAC,SAAS,CAAC;qBACnE;iBACF;aACF;YACD,IAAI,OAAO,EAAE;gBACX,MAAM,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC,GAAG,OAAO,CAAC;gBAElE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,IAAI,uBAAA,IAAI,6FAAgC,MAApC,IAAI,CAAkC,EAAE;oBACrE,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;iBAC9D;aACF;YAED,IAAI,UAAU,EAAE;gBACd,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU;oBAAE,MAAM,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE,CAAC;gBAE/D,KAAK,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,GAAG,IAAI,EAAE,IAAI,UAAU,EAAE;oBACnE,MAAM,SAAS,GAAG,OAAC,MAAM,CAAC,OAAO,CAAC,UAAU,EAAC,KAAK,SAAL,KAAK,IAChD,EAAoD,EAAC,CAAC;oBACxD,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;oBAC/B,IAAI,EAAE;wBAAE,SAAS,CAAC,EAAE,GAAG,EAAE,CAAC;oBAC1B,IAAI,IAAI;wBAAE,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC;oBAChC,IAAI,EAAE;wBAAE,SAAS,CAAC,QAAQ,KAAlB,SAAS,CAAC,QAAQ,GAAK,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,EAAC;oBACtE,IAAI,EAAE,EAAE,IAAI;wBAAE,SAAS,CAAC,QAAS,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;oBACjD,IAAI,EAAE,EAAE,SAAS,EAAE;wBACjB,SAAS,CAAC,QAAS,CAAC,SAAS,IAAI,EAAE,CAAC,SAAS,CAAC;wBAE9C,IAAI,mBAAmB,CAAC,uBAAA,IAAI,oCAAQ,EAAE,SAAS,CAAC,EAAE;4BAChD,SAAS,CAAC,QAAS,CAAC,gBAAgB,GAAG,YAAY,CAAC,SAAS,CAAC,QAAS,CAAC,SAAS,CAAC,CAAC;yBACpF;qBACF;iBACF;aACF;SACF;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC,EAEA,MAAM,CAAC,aAAa,EAAC;QACpB,MAAM,SAAS,GAA0B,EAAE,CAAC;QAC5C,MAAM,SAAS,GAGT,EAAE,CAAC;QACT,IAAI,IAAI,GAAG,KAAK,CAAC;QAEjB,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YACzB,MAAM,MAAM,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;YACjC,IAAI,MAAM,EAAE;gBACV,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aACvB;iBAAM;gBACL,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACvB;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YAClB,IAAI,GAAG,IAAI,CAAC;YACZ,KAAK,MAAM,MAAM,IAAI,SAAS,EAAE;gBAC9B,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;aAC3B;YACD,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;YACvB,IAAI,GAAG,IAAI,CAAC;YACZ,KAAK,MAAM,MAAM,IAAI,SAAS,EAAE;gBAC9B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;aACpB;YACD,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;YACvB,IAAI,GAAG,IAAI,CAAC;YACZ,KAAK,MAAM,MAAM,IAAI,SAAS,EAAE;gBAC9B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;aACpB;YACD,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,IAAI,EAAE,KAAK,IAAkD,EAAE;gBAC7D,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;oBACrB,IAAI,IAAI,EAAE;wBACR,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;qBACzC;oBACD,OAAO,IAAI,OAAO,CAAkC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,CACtE,SAAS,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CACpC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;iBAC/F;gBACD,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,EAAG,CAAC;gBACjC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;YACvC,CAAC;YACD,MAAM,EAAE,KAAK,IAAI,EAAE;gBACjB,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;YAC1C,CAAC;SACF,CAAC;IACJ,CAAC;IAED,gBAAgB;QACd,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAClF,OAAO,MAAM,CAAC,gBAAgB,EAAE,CAAC;IACnC,CAAC;CACF;AAED,SAAS,sBAAsB,CAC7B,QAAgC,EAChC,MAAyC;IAEzC,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,kBAAkB,EAAE,GAAG,IAAI,EAAE,GAAG,QAAQ,CAAC;IAC9E,MAAM,UAAU,GAAmB;QACjC,GAAG,IAAI;QACP,EAAE;QACF,OAAO,EAAE,OAAO,CAAC,GAAG,CAClB,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,UAAU,EAAE,EAAyB,EAAE;YACpF,IAAI,CAAC,aAAa,EAAE;gBAClB,MAAM,IAAI,WAAW,CAAC,oCAAoC,KAAK,EAAE,CAAC,CAAC;aACpE;YAED,MAAM,EAAE,OAAO,GAAG,IAAI,EAAE,aAAa,EAAE,UAAU,EAAE,GAAG,WAAW,EAAE,GAAG,OAAO,CAAC;YAC9E,MAAM,IAAI,GAAG,OAAO,CAAC,IAAmB,CAAC,CAAC,qHAAqH;YAC/J,IAAI,CAAC,IAAI,EAAE;gBACT,MAAM,IAAI,WAAW,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;aAC3D;YAED,IAAI,aAAa,EAAE;gBACjB,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,aAAa,CAAC;gBAChD,IAAI,IAAI,IAAI,IAAI,EAAE;oBAChB,MAAM,IAAI,WAAW,CAAC,8CAA8C,KAAK,EAAE,CAAC,CAAC;iBAC9E;gBAED,IAAI,CAAC,IAAI,EAAE;oBACT,MAAM,IAAI,WAAW,CAAC,yCAAyC,KAAK,EAAE,CAAC,CAAC;iBACzE;gBAED,OAAO;oBACL,GAAG,UAAU;oBACb,OAAO,EAAE;wBACP,OAAO;wBACP,aAAa,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE;wBACxC,IAAI;wBACJ,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,IAAI;qBACjC;oBACD,aAAa;oBACb,KAAK;oBACL,QAAQ;iBACT,CAAC;aACH;YAED,IAAI,UAAU,EAAE;gBACd,OAAO;oBACL,GAAG,UAAU;oBACb,KAAK;oBACL,aAAa;oBACb,QAAQ;oBACR,OAAO,EAAE;wBACP,GAAG,WAAW;wBACd,IAAI;wBACJ,OAAO;wBACP,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,IAAI;wBAChC,UAAU,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC,EAAE,EAAE;4BAC1C,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,QAAQ,EAAE,GAAG,SAAS,CAAC;4BAC1D,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;4BACtD,IAAI,EAAE,IAAI,IAAI,EAAE;gCACd,MAAM,IAAI,WAAW,CAAC,mBAAmB,KAAK,gBAAgB,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;6BAC1F;4BACD,IAAI,IAAI,IAAI,IAAI,EAAE;gCAChB,MAAM,IAAI,WAAW,CAAC,mBAAmB,KAAK,gBAAgB,CAAC,WAAW,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;6BAC5F;4BACD,IAAI,IAAI,IAAI,IAAI,EAAE;gCAChB,MAAM,IAAI,WAAW,CACnB,mBAAmB,KAAK,gBAAgB,CAAC,oBAAoB,GAAG,CAAC,QAAQ,CAAC,EAAE,CAC7E,CAAC;6BACH;4BACD,IAAI,IAAI,IAAI,IAAI,EAAE;gCAChB,MAAM,IAAI,WAAW,CACnB,mBAAmB,KAAK,gBAAgB,CAAC,yBAAyB,GAAG,CAAC,QAAQ,CAAC,EAAE,CAClF,CAAC;6BACH;4BAED,OAAO,EAAE,GAAG,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,GAAG,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC;wBACnF,CAAC,CAAC;qBACH;iBACF,CAAC;aACH;YACD,OAAO;gBACL,GAAG,UAAU;gBACb,OAAO,EAAE,EAAE,GAAG,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,IAAI,EAAE;gBAC5E,aAAa;gBACb,KAAK;gBACL,QAAQ;aACT,CAAC;QACJ,CAAC,CACF;QACD,OAAO;QACP,KAAK;QACL,MAAM,EAAE,iBAAiB;QACzB,GAAG,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,kBAAkB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;KACtD,CAAC;IAEF,OAAO,wBAAwB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AACtD,CAAC;AAED,SAAS,GAAG,CAAC,CAAU;IACrB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAC3B,CAAC;AA0JD;;;;GAIG;AACH,SAAS,aAAa,CAAe,GAAqB;IACxD,OAAO;AACT,CAAC;AAED,SAAS,WAAW,CAAC,EAAS,IAAG,CAAC"}