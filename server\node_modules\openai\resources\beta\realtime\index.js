"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.TranscriptionSessions = exports.Sessions = exports.Realtime = void 0;
var realtime_1 = require("./realtime.js");
Object.defineProperty(exports, "Realtime", { enumerable: true, get: function () { return realtime_1.Realtime; } });
var sessions_1 = require("./sessions.js");
Object.defineProperty(exports, "Sessions", { enumerable: true, get: function () { return sessions_1.Sessions; } });
var transcription_sessions_1 = require("./transcription-sessions.js");
Object.defineProperty(exports, "TranscriptionSessions", { enumerable: true, get: function () { return transcription_sessions_1.TranscriptionSessions; } });
//# sourceMappingURL=index.js.map