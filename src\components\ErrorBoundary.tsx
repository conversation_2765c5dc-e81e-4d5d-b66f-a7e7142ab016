import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Box, Heading, Text, Button, VStack } from '@chakra-ui/react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Uncaught error:', error, errorInfo);
  }

  public render() {
    if (this.state.hasError) {
      return (
        <Box 
          minH="100vh" 
          display="flex" 
          alignItems="center" 
          justifyContent="center"
          bg="gray.50"
          p={8}
        >
          <VStack spacing={6} textAlign="center" maxW="md">
            <Heading size="lg" color="red.500">
              🚨 Что-то пошло не так
            </Heading>
            <Text color="gray.600">
              Произошла ошибка при загрузке страницы. Попробуйте обновить страницу.
            </Text>
            {this.state.error && (
              <Text fontSize="sm" color="gray.500" fontFamily="mono">
                {this.state.error.message}
              </Text>
            )}
            <Button 
              colorScheme="blue" 
              onClick={() => window.location.reload()}
            >
              Обновить страницу
            </Button>
          </VStack>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
