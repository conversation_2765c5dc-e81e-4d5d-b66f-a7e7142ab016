import {
  Box,
  Flex,
  Text,
  IconButton,
  Button,
  Stack,
  Collapse,
  Icon,
  Link,
  Popover,
  PopoverTrigger,
  PopoverContent,
  useColorModeValue,
  useBreakpointValue,
  useDisclosure
} from '@chakra-ui/react';
import {
  HamburgerIcon,
  CloseIcon,
  ChevronDownIcon,
  ChevronRightIcon
} from '@chakra-ui/icons';
import { Link as RouterLink } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

export default function Navbar() {
  const { isOpen, onToggle } = useDisclosure();
  const { user, logout, isAuthenticated } = useAuth();

  return (
    <Box position="relative">
      <Flex
        bg={useColorModeValue('white/95', 'gray.900/95')}
        backdropFilter="blur(10px)"
        color={useColorModeValue('gray.700', 'gray.100')}
        h={'64px'}
        py={{ base: 2 }}
        px={{ base: 4, md: 8 }}
        borderBottom={1}
        borderStyle={'solid'}
        borderColor={useColorModeValue('gray.200', 'gray.700')}
        align={'center'}
        position="sticky"
        top={0}
        zIndex={1000}
        boxShadow={useColorModeValue('sm', 'dark-sm')}>
        <Flex
          flex={{ base: 1, md: 'auto' }}
          ml={{ base: -2 }}
          display={{ base: 'flex', md: 'none' }}>
          <IconButton
            onClick={onToggle}
            icon={
              isOpen ? <CloseIcon w={3} h={3} /> : <HamburgerIcon w={5} h={5} />
            }
            variant={'ghost'}
            aria-label={'Toggle Navigation'}
          />
        </Flex>
        <Flex flex={{ base: 1 }} justify={{ base: 'center', md: 'start' }}>
          <Flex
            as={RouterLink}
            to="/"
            align="center"
            mr={1}
          >
            <Box
              bgGradient="linear(to-r, primary.500, accent.500)"
              color="white"
              borderRadius="xl"
              p={2}
              mr={3}
              display="flex"
              alignItems="center"
              justifyContent="center"
              boxShadow="md"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 4L4 8L12 12L20 8L12 4Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M4 12L12 16L20 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M4 16L12 20L20 16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </Box>
            <Box>
              <Text
                fontFamily={'heading'}
                color={useColorModeValue('gray.800', 'white')}
                fontWeight="bold"
                fontSize={{ base: 'md', md: 'lg' }}
                lineHeight="1.2"
                bgGradient="linear(to-r, primary.500, accent.500)"
                bgClip="text"
              >
                Ozon Price
              </Text>
              <Text
                fontSize={{ base: 'xs', md: 'sm' }}
                color={useColorModeValue('gray.500', 'gray.400')}
                fontWeight="medium"
                lineHeight="1"
                mt="-1"
              >
                Optimizer Pro
              </Text>
            </Box>
          </Flex>

          <Flex display={{ base: 'none', md: 'flex' }} ml={2}>
            <DesktopNav isAuthenticated={isAuthenticated} />
          </Flex>
        </Flex>

        <Stack
          flex={{ base: 1, md: 0 }}
          justify={'flex-end'}
          direction={'row'}
          spacing={2}
          position="relative"
          zIndex={1001}>
          {isAuthenticated ? (
            <>
              <Button
                as={RouterLink}
                fontSize={'sm'}
                fontWeight={500}
                variant={'ghost'}
                to="/profile"
                px={4}
                py={2}
                borderRadius="lg"
                _hover={{
                  bg: useColorModeValue('gray.100', 'gray.700'),
                  transform: 'translateY(-1px)',
                }}>
                Профиль
              </Button>
              <Button
                display={{ base: 'none', md: 'inline-flex' }}
                fontSize={'sm'}
                fontWeight={600}
                color={'white'}
                bgGradient="linear(to-r, red.400, red.500)"
                onClick={logout}
                px={4}
                py={2}
                borderRadius="lg"
                boxShadow="md"
                _hover={{
                  bgGradient: "linear(to-r, red.500, red.600)",
                  transform: 'translateY(-1px)',
                  boxShadow: 'lg',
                }}>
                Выйти
              </Button>
            </>
          ) : (
            <>
              <Button
                as={RouterLink}
                fontSize={'sm'}
                fontWeight={600}
                bgGradient="linear(to-r, primary.500, accent.500)"
                color="white"
                to="/login"
                px={6}
                py={2}
                borderRadius="lg"
                boxShadow="md"
                mr={2}
                _hover={{
                  bgGradient: "linear(to-r, primary.600, accent.600)",
                  transform: 'translateY(-1px)',
                  boxShadow: 'lg',
                }}>
                Войти
              </Button>
              <Button
                as={RouterLink}
                display={{ base: 'none', md: 'inline-flex' }}
                fontSize={'sm'}
                fontWeight={600}
                variant="outline"
                borderColor="primary.500"
                color="primary.500"
                to="/register"
                px={6}
                py={2}
                borderRadius="lg"
                _hover={{
                  bg: useColorModeValue('primary.50', 'primary.900/20'),
                  transform: 'translateY(-1px)',
                  borderColor: 'primary.600',
                }}>
                Регистрация
              </Button>
            </>
          )}
        </Stack>
      </Flex>

      <Collapse in={isOpen} animateOpacity>
        <MobileNav isAuthenticated={isAuthenticated} logout={logout} />
      </Collapse>
    </Box>
  );
}

const DesktopNav = ({ isAuthenticated }: { isAuthenticated: boolean }) => {
  const linkColor = useColorModeValue('gray.600', 'gray.200');
  const linkHoverColor = useColorModeValue('gray.800', 'white');
  const popoverContentBgColor = useColorModeValue('white', 'gray.800');

  return (
    <Stack direction={'row'} spacing={2}>
      {NAV_ITEMS.filter(item => !item.requiresAuth || isAuthenticated).map((navItem) => (
        <Box key={navItem.label}>
          <Popover trigger={'hover'} placement={'bottom'} strategy={'fixed'}>
            <PopoverTrigger>
              <Link
                as={RouterLink}
                p={1}
                to={navItem.href ?? '#'}
                fontSize={'xs'}
                fontWeight={500}
                color={linkColor}
                _hover={{
                  textDecoration: 'none',
                  color: linkHoverColor,
                }}>
                {navItem.label}
              </Link>
            </PopoverTrigger>

            {navItem.children && (
              <PopoverContent
                border={0}
                boxShadow={'md'}
                bg={popoverContentBgColor}
                p={2}
                rounded={'md'}
                minW={'xs'}
                zIndex={1000}>
                <Stack spacing={0}>
                  {navItem.children.map((child) => (
                    <DesktopSubNav key={child.label} {...child} />
                  ))}
                </Stack>
              </PopoverContent>
            )}
          </Popover>
        </Box>
      ))}
    </Stack>
  );
};

const DesktopSubNav = ({ label, href, subLabel }: NavItem) => {
  return (
    <Link
      as={RouterLink}
      to={href ?? '#'}
      role={'group'}
      display={'block'}
      p={1}
      fontSize="xs"
      rounded={'md'}
      _hover={{ bg: useColorModeValue('blue.50', 'gray.900') }}>
      <Stack direction={'row'} align={'center'} spacing={1}>
        <Box>
          <Text
            transition={'all .3s ease'}
            _groupHover={{ color: 'blue.400' }}
            fontWeight={500}>
            {label}
          </Text>
          {subLabel && <Text fontSize={'xs'} color="gray.500">{subLabel}</Text>}
        </Box>
        <Flex
          transition={'all .3s ease'}
          transform={'translateX(-5px)'}
          opacity={0}
          _groupHover={{ opacity: '100%', transform: 'translateX(0)' }}
          justify={'flex-end'}
          align={'center'}
          flex={1}>
          <Icon color={'blue.400'} w={3} h={3} as={ChevronRightIcon} />
        </Flex>
      </Stack>
    </Link>
  );
};

const MobileNav = ({ isAuthenticated, logout }: { isAuthenticated: boolean, logout: () => void }) => {
  return (
    <Stack
      bg={useColorModeValue('white', 'gray.800')}
      p={4}
      display={{ md: 'none' }}>
      {NAV_ITEMS.filter(item => !item.requiresAuth || isAuthenticated).map((navItem) => (
        <MobileNavItem key={navItem.label} {...navItem} />
      ))}
      {isAuthenticated && (
        <Box py={2} onClick={logout}>
          <Text fontWeight={600} color={'red.400'}>
            Выйти
          </Text>
        </Box>
      )}
    </Stack>
  );
};

const MobileNavItem = ({ label, children, href }: NavItem) => {
  const { isOpen, onToggle } = useDisclosure();

  return (
    <Stack spacing={4} onClick={children && onToggle}>
      <Flex
        py={2}
        as={RouterLink}
        to={href ?? '#'}
        justify={'space-between'}
        align={'center'}
        _hover={{
          textDecoration: 'none',
        }}>
        <Text
          fontWeight={600}
          color={useColorModeValue('gray.600', 'gray.200')}>
          {label}
        </Text>
        {children && (
          <Icon
            as={ChevronDownIcon}
            transition={'all .25s ease-in-out'}
            transform={isOpen ? 'rotate(180deg)' : ''}
            w={6}
            h={6}
          />
        )}
      </Flex>

      <Collapse in={isOpen} animateOpacity style={{ marginTop: '0!important' }}>
        <Stack
          mt={2}
          pl={4}
          borderLeft={1}
          borderStyle={'solid'}
          borderColor={useColorModeValue('gray.200', 'gray.700')}
          align={'start'}>
          {children &&
            children.map((child) => (
              <Link key={child.label} py={2} as={RouterLink} to={child.href ?? '#'}>
                {child.label}
              </Link>
            ))}
        </Stack>
      </Collapse>
    </Stack>
  );
};

interface NavItem {
  label: string;
  subLabel?: string;
  children?: Array<NavItem>;
  href?: string;
  requiresAuth?: boolean;
}

const NAV_ITEMS: Array<NavItem> = [
  {
    label: 'Главная',
    href: '/',
  },
  {
    label: 'Товары',
    href: '/products',
    requiresAuth: true,
  },
  {
    label: 'Связанные товары',
    href: '/linked-products',
    requiresAuth: true,
  },
  {
    label: 'Изменение в цене',
    href: '/price-changes',
    requiresAuth: true,
  },
  {
    label: 'Управление',
    requiresAuth: true,
    children: [
      {
        label: 'Стратегии',
        href: '/strategies',
      },
      {
        label: 'Алерты',
        href: '/alerts',
      },
      {
        label: 'ИИ-анализ',
        href: '/ai-analysis',
      },
    ],
  },
  {
    label: 'Система',
    requiresAuth: true,
    children: [
      {
        label: 'Настройки',
        href: '/settings',
      },
      {
        label: 'Пользователи',
        href: '/users',
      },
      {
        label: 'Мониторинг',
        href: '/monitoring',
      },
      {
        label: 'Интеграция',
        href: '/integration-guide',
      },
    ],
  },
];
