import { useState, useEffect } from 'react';
import { IconButton, useColorMode, useColorModeValue, Tooltip } from '@chakra-ui/react';
import { SunIcon, MoonIcon } from '@chakra-ui/icons';

interface ThemeToggleProps {
  size?: string;
  variant?: string;
  isCompact?: boolean;
}

export default function ThemeToggle({
  size = 'md',
  variant = 'ghost',
  isCompact = false
}: ThemeToggleProps) {
  const { colorMode, toggleColorMode } = useColorMode();
  const [mounted, setMounted] = useState(false);

  // Цвета для иконки и hover эффекта
  const iconColor = useColorModeValue('gray.700', 'yellow.300');
  const hoverBg = useColorModeValue('gray.100', 'gray.700');

  // Эффект для предотвращения гидратации
  useEffect(() => {
    setMounted(true);
  }, []);

  // Если компонент не смонтирован, возвращаем пустой div для предотвращения ошибок гидратации
  if (!mounted) return <div style={{ width: isCompact ? '32px' : '40px', height: isCompact ? '32px' : '40px' }} />;

  return (
    <Tooltip
      label={colorMode === 'light' ? 'Включить тёмную тему' : 'Включить светлую тему'}
      placement="bottom"
      hasArrow
    >
      <IconButton
        aria-label={colorMode === 'light' ? 'Включить тёмную тему' : 'Включить светлую тему'}
        icon={colorMode === 'light' ? <MoonIcon color={iconColor} /> : <SunIcon color={iconColor} />}
        onClick={toggleColorMode}
        size={size}
        variant={variant}
        borderRadius="md"
        _hover={{ bg: hoverBg }}
      />
    </Tooltip>
  );
}
