import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dalOverlay,
  ModalContent,
  <PERSON>dal<PERSON>eader,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  Input,
  InputGroup,
  InputLeftElement,
  VStack,
  HStack,
  Box,
  Text,
  Badge,
  Flex,
  Spacer,
  useToast,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Select,
  Alert,
  AlertIcon,
  AlertDescription,
  Spinner,
  Grid,
  IconButton
} from '@chakra-ui/react';
import {
  FiSearch,
  FiPlus,
  FiAlertTriangle,
  FiStar,
  FiPackage,
  FiBrain,
  FiExternalLink
} from 'react-icons/fi';
import {
  CompetitorSearchResult,
  CompetitorType
} from '../../types';
import competitorApi from '../../services/competitorApi';

interface CompetitorSearchModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCompetitorAdded: () => void;
}

const CompetitorSearchModal: React.FC<CompetitorSearchModalProps> = ({
  isOpen,
  onClose,
  onCompetitorAdded
}) => {
  // Состояние
  const [searchQuery, setSearchQuery] = useState('');
  const [searchType, setSearchType] = useState<'product' | 'shop'>('product');
  const [category, setCategory] = useState('');
  const [searchResults, setSearchResults] = useState<CompetitorSearchResult[]>([]);
  const [aiRecommendations, setAiRecommendations] = useState<CompetitorSearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [adding, setAdding] = useState<string | null>(null);

  // Хуки
  const toast = useToast();

  // Загрузка ИИ-рекомендаций при открытии модала
  useEffect(() => {
    if (isOpen) {
      loadAIRecommendations();
    }
  }, [isOpen]);

  // Загрузка ИИ-рекомендаций
  const loadAIRecommendations = async () => {
    try {
      const recommendations = await competitorApi.getAICompetitorRecommendations();
      setAiRecommendations(recommendations);
    } catch (error) {
      console.error('Ошибка загрузки ИИ-рекомендаций:', error);
    }
  };

  // Поиск конкурентов
  const handleSearch = async () => {
    if (!searchQuery.trim()) return;

    try {
      setLoading(true);
      let results: CompetitorSearchResult[] = [];

      if (searchType === 'product') {
        results = await competitorApi.searchCompetitorsByProduct(searchQuery, category || undefined);
      } else {
        results = await competitorApi.searchCompetitorsByShop(searchQuery);
      }

      setSearchResults(results);
    } catch (error) {
      toast({
        title: 'Ошибка поиска',
        description: 'Не удалось найти конкурентов',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // Добавление конкурента
  const handleAddCompetitor = async (shopId: string, competitorType: CompetitorType = CompetitorType.SECONDARY) => {
    try {
      setAdding(shopId);
      await competitorApi.addCompetitor(shopId, competitorType);

      toast({
        title: 'Конкурент добавлен',
        description: 'Конкурент успешно добавлен в систему мониторинга',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      onCompetitorAdded();
      onClose();
    } catch (error) {
      toast({
        title: 'Ошибка добавления',
        description: 'Не удалось добавить конкурента',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setAdding(null);
    }
  };

  // Компонент карточки конкурента
  const CompetitorCard: React.FC<{ competitor: CompetitorSearchResult; showAIBadge?: boolean }> = ({
    competitor,
    showAIBadge = false
  }) => (
    <Box mb={3} borderWidth="1px" borderRadius="lg" p={4}>
        <Flex align="center" mb={3}>
          <Box>
            <Text fontWeight="bold" fontSize="lg">
              {competitor.name}
            </Text>
            <Text fontSize="sm" color="gray.500">
              ID: {competitor.shopId}
            </Text>
          </Box>
          <Spacer />
          <VStack spacing={1} align="end">
            {showAIBadge && (
              <Badge colorScheme="purple" leftIcon={<FiBrain size={12} />}>
                ИИ рекомендует
              </Badge>
            )}
            {competitor.suspiciousActivity && (
              <Badge colorScheme="red" leftIcon={<FiAlertTriangle size={12} />}>
                Подозрительная активность
              </Badge>
            )}
            {competitor.isAlreadyAdded && (
              <Badge colorScheme="green">
                Уже добавлен
              </Badge>
            )}
          </VStack>
        </Flex>

        <Grid templateColumns="repeat(3, 1fr)" gap={4} mb={3} fontSize="sm">
          <Box>
            <Text color="gray.500">Рейтинг</Text>
            <Flex align="center">
              <FiStar size={14} fill="gold" color="gold" />
              <Text fontWeight="medium" ml={1}>
                {competitor.rating.toFixed(1)}
              </Text>
            </Flex>
          </Box>
          <Box>
            <Text color="gray.500">Товаров</Text>
            <Flex align="center">
              <FiPackage size={14} />
              <Text fontWeight="medium" ml={1}>
                {competitor.totalProducts}
              </Text>
            </Flex>
          </Box>
          <Box>
            <Text color="gray.500">Совпадений</Text>
            <Text fontWeight="medium" color="blue.500">
              {competitor.matchingProducts}
            </Text>
          </Box>
        </Grid>

        <Flex gap={2}>
          <Button
            size="sm"
            colorScheme="blue"
            leftIcon={<FiPlus size={14} />}
            onClick={() => handleAddCompetitor(competitor.shopId)}
            isLoading={adding === competitor.shopId}
            isDisabled={competitor.isAlreadyAdded}
          >
            {competitor.isAlreadyAdded ? 'Добавлен' : 'Добавить'}
          </Button>

          <Select size="sm" maxW="150px" defaultValue={CompetitorType.SECONDARY}>
            <option value={CompetitorType.MAIN}>Основной</option>
            <option value={CompetitorType.SECONDARY}>Второстепенный</option>
            <option value={CompetitorType.AGGRESSIVE}>Агрессивный</option>
          </Select>

          <IconButton
            size="sm"
            aria-label="Открыть магазин"
            icon={<FiExternalLink size={14} />}
            variant="outline"
            onClick={() => window.open(`https://ozon.ru/seller/${competitor.shopId}`, '_blank')}
          />
        </Flex>
    </Box>
  );

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="4xl" scrollBehavior="inside">
      <ModalOverlay />
      <ModalContent maxH="90vh">
        <ModalHeader>🔍 Поиск и добавление конкурентов</ModalHeader>
        <ModalCloseButton />

        <ModalBody>
          <Tabs>
            <TabList>
              <Tab>🔍 Поиск</Tab>
              <Tab>🤖 ИИ-рекомендации</Tab>
            </TabList>

            <TabPanels>
              {/* Вкладка поиска */}
              <TabPanel px={0}>
                <VStack spacing={4} align="stretch">
                  {/* Настройки поиска */}
                  <HStack>
                    <Select
                      value={searchType}
                      onChange={(e) => setSearchType(e.target.value as 'product' | 'shop')}
                      maxW="200px"
                    >
                      <option value="product">По товару</option>
                      <option value="shop">По магазину</option>
                    </Select>

                    {searchType === 'product' && (
                      <Select
                        placeholder="Категория (опционально)"
                        value={category}
                        onChange={(e) => setCategory(e.target.value)}
                        maxW="250px"
                      >
                        <option value="electronics">Электроника</option>
                        <option value="clothing">Одежда</option>
                        <option value="home">Дом и сад</option>
                        <option value="beauty">Красота и здоровье</option>
                        <option value="sports">Спорт и отдых</option>
                      </Select>
                    )}
                  </HStack>

                  {/* Строка поиска */}
                  <HStack>
                    <InputGroup>
                      <InputLeftElement>
                        <FiSearch size={20} />
                      </InputLeftElement>
                      <Input
                        placeholder={
                          searchType === 'product'
                            ? "Введите название товара (например: iPhone 15 Pro)"
                            : "Введите название магазина"
                        }
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                      />
                    </InputGroup>
                    <Button
                      colorScheme="blue"
                      onClick={handleSearch}
                      isLoading={loading}
                      minW="100px"
                    >
                      Найти
                    </Button>
                  </HStack>

                  {/* Результаты поиска */}
                  {loading && (
                    <Flex justify="center" py={8}>
                      <Spinner size="lg" />
                    </Flex>
                  )}

                  {!loading && searchResults.length === 0 && searchQuery && (
                    <Alert status="info">
                      <AlertIcon />
                      <AlertDescription>
                        Конкуренты не найдены. Попробуйте изменить запрос.
                      </AlertDescription>
                    </Alert>
                  )}

                  {!loading && searchResults.length > 0 && (
                    <Box>
                      <Text fontWeight="bold" mb={3}>
                        Найдено конкурентов: {searchResults.length}
                      </Text>
                      {searchResults.map((competitor) => (
                        <CompetitorCard
                          key={competitor.shopId}
                          competitor={competitor}
                        />
                      ))}
                    </Box>
                  )}
                </VStack>
              </TabPanel>

              {/* Вкладка ИИ-рекомендаций */}
              <TabPanel px={0}>
                <VStack spacing={4} align="stretch">
                  <Alert status="info">
                    <AlertIcon />
                    <AlertDescription>
                      ИИ анализирует ваш ассортимент и рекомендует конкурентов на основе схожести товаров и ценовой политики.
                    </AlertDescription>
                  </Alert>

                  {aiRecommendations.length === 0 ? (
                    <Text color="gray.500" textAlign="center" py={8}>
                      Загрузка рекомендаций...
                    </Text>
                  ) : (
                    <Box>
                      <Text fontWeight="bold" mb={3}>
                        🤖 ИИ рекомендует добавить ({aiRecommendations.length}):
                      </Text>
                      {aiRecommendations.map((competitor) => (
                        <CompetitorCard
                          key={competitor.shopId}
                          competitor={competitor}
                          showAIBadge={true}
                        />
                      ))}
                    </Box>
                  )}
                </VStack>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" onClick={onClose}>
            Закрыть
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default CompetitorSearchModal;
