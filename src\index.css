@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100 font-sans antialiased;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    min-height: 100vh;
    min-width: 320px;
  }

  /* Улучшенная прокрутка */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800 rounded-full;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full hover:bg-gray-400 dark:hover:bg-gray-500;
  }

  /* Плавные переходы */
  * {
    transition: all 0.2s ease-out;
  }
}

@layer components {
  /* Современные кнопки */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-semibold transition-all duration-200 ease-out;
  }

  .btn-primary {
    @apply bg-primary-500 text-white shadow-md hover:bg-primary-600 hover:shadow-lg hover:-translate-y-0.5;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-300 dark:hover:bg-gray-600;
  }

  .btn-outline {
    @apply border-2 border-primary-500 text-primary-500 bg-transparent hover:bg-primary-500 hover:text-white;
  }

  /* Современные карточки */
  .card {
    @apply bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-xl border border-gray-200 dark:border-gray-700 shadow-md transition-all duration-200 ease-out hover:shadow-lg hover:-translate-y-1;
  }

  /* Современные поля ввода */
  .input {
    @apply flex h-10 w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm placeholder:text-gray-500 dark:placeholder:text-gray-400;
  }

  /* Бейджи */
  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200;
  }

  .badge-success {
    @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
  }

  .badge-error {
    @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
  }

  /* Градиентные фоны */
  .gradient-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-600;
  }

  .gradient-hero {
    @apply bg-gradient-to-br from-primary-500 via-accent-500 to-primary-600;
  }

  /* Текстовые утилиты */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-500 to-accent-500 bg-clip-text text-transparent;
  }

  /* Состояния загрузки */
  .skeleton {
    @apply bg-gray-200 dark:bg-gray-700 rounded animate-pulse;
  }
}
