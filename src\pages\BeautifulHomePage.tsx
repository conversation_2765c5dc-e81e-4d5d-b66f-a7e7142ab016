import React from 'react';
import { Link } from 'react-router-dom';

export default function BeautifulHomePage() {
  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
      color: 'white'
    }}>
      {/* Header */}
      <header style={{
        padding: '1.5rem 2rem',
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(20px)',
        borderBottom: '1px solid rgba(255, 255, 255, 0.2)'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          maxWidth: '1200px',
          margin: '0 auto'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '1rem'
          }}>
            <div style={{
              width: '50px',
              height: '50px',
              background: 'linear-gradient(135deg, #ff6b6b, #4ecdc4)',
              borderRadius: '15px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '24px',
              boxShadow: '0 8px 32px rgba(0,0,0,0.3)'
            }}>
              🚀
            </div>
            <div>
              <h1 style={{
                margin: 0,
                fontSize: '1.8rem',
                fontWeight: '800',
                letterSpacing: '-0.02em'
              }}>
                Ozon Price Optimizer
              </h1>
              <p style={{
                margin: 0,
                fontSize: '0.9rem',
                opacity: 0.8
              }}>
                ИИ-платформа для автоматизации цен
              </p>
            </div>
          </div>

          <nav style={{ display: 'flex', gap: '1rem' }}>
            <Link
              to="/demo"
              style={{
                color: 'white',
                textDecoration: 'none',
                padding: '0.75rem 1.5rem',
                border: '2px solid rgba(255, 255, 255, 0.3)',
                borderRadius: '12px',
                fontWeight: '600',
                background: 'rgba(255, 255, 255, 0.1)',
                transition: 'all 0.3s ease'
              }}
            >
              Демо
            </Link>
            <Link
              to="/register"
              style={{
                color: '#1a1a1a',
                textDecoration: 'none',
                padding: '0.75rem 1.5rem',
                background: 'white',
                borderRadius: '12px',
                fontWeight: '700',
                boxShadow: '0 4px 20px rgba(0,0,0,0.2)',
                transition: 'all 0.3s ease'
              }}
            >
              Начать бесплатно
            </Link>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <main style={{ padding: '6rem 2rem 4rem' }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          textAlign: 'center'
        }}>
          {/* Badge */}
          <div style={{
            background: 'rgba(255, 255, 255, 0.15)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            padding: '0.75rem 1.5rem',
            borderRadius: '50px',
            display: 'inline-flex',
            alignItems: 'center',
            gap: '0.5rem',
            marginBottom: '3rem',
            fontSize: '0.95rem',
            fontWeight: '600',
            backdropFilter: 'blur(10px)'
          }}>
            <span>🎯</span>
            ИИ-платформа нового поколения для Ozon
          </div>

          {/* Main Heading */}
          <h1 style={{
            fontSize: 'clamp(3rem, 6vw, 5.5rem)',
            fontWeight: '900',
            margin: '0 0 2rem 0',
            lineHeight: '1.1',
            letterSpacing: '-0.02em'
          }}>
            Увеличьте прибыль на{' '}
            <span style={{
              background: 'linear-gradient(135deg, #4ecdc4, #44a08d)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>
              40%
            </span>
            <br />
            с помощью ИИ-автоматизации
          </h1>

          {/* Subtitle */}
          <p style={{
            fontSize: '1.3rem',
            margin: '0 0 3rem 0',
            maxWidth: '800px',
            marginLeft: 'auto',
            marginRight: 'auto',
            opacity: '0.9',
            lineHeight: '1.6'
          }}>
            Революционная платформа для продавцов Ozon. Защита от демпинга,
            умная аналитика и полная автоматизация ценообразования.
          </p>

          {/* CTA Buttons */}
          <div style={{
            display: 'flex',
            gap: '1.5rem',
            justifyContent: 'center',
            flexWrap: 'wrap',
            marginBottom: '4rem'
          }}>
            <Link
              to="/register"
              style={{
                background: 'linear-gradient(135deg, #ff6b6b, #4ecdc4)',
                color: 'white',
                textDecoration: 'none',
                padding: '1.25rem 2.5rem',
                borderRadius: '16px',
                fontSize: '1.2rem',
                fontWeight: '700',
                display: 'inline-flex',
                alignItems: 'center',
                gap: '0.5rem',
                boxShadow: '0 10px 40px rgba(0,0,0,0.2)',
                transition: 'all 0.3s ease'
              }}
            >
              <span>Начать бесплатно</span>
              <span>🚀</span>
            </Link>

            <Link
              to="/demo"
              style={{
                background: 'rgba(255, 255, 255, 0.1)',
                color: 'white',
                textDecoration: 'none',
                padding: '1.25rem 2.5rem',
                borderRadius: '16px',
                fontSize: '1.2rem',
                fontWeight: '600',
                border: '2px solid rgba(255, 255, 255, 0.2)',
                display: 'inline-flex',
                alignItems: 'center',
                gap: '0.5rem',
                backdropFilter: 'blur(10px)',
                transition: 'all 0.3s ease'
              }}
            >
              <span>Посмотреть демо</span>
              <span>▶️</span>
            </Link>
          </div>

          {/* Stats */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '2rem',
            marginTop: '4rem'
          }}>
            {[
              { number: '40%', text: 'Рост прибыли', icon: '📈' },
              { number: '500+', text: 'Клиентов', icon: '👥' },
              { number: '24/7', text: 'Мониторинг', icon: '🔍' }
            ].map((stat, index) => (
              <div
                key={index}
                style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  padding: '2rem',
                  borderRadius: '20px',
                  textAlign: 'center',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  transition: 'all 0.3s ease'
                }}
              >
                <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>
                  {stat.icon}
                </div>
                <div style={{
                  fontSize: '2.5rem',
                  fontWeight: 'bold',
                  color: '#4ecdc4',
                  marginBottom: '0.5rem'
                }}>
                  {stat.number}
                </div>
                <div style={{ fontSize: '1rem', opacity: '0.9' }}>
                  {stat.text}
                </div>
              </div>
            ))}
          </div>
        </div>
      </main>

      {/* Features Section */}
      <section style={{
        background: 'rgba(255, 255, 255, 0.05)',
        padding: '4rem 2rem',
        marginTop: '2rem'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          textAlign: 'center'
        }}>
          <h2 style={{
            fontSize: '2.5rem',
            fontWeight: 'bold',
            marginBottom: '3rem'
          }}>
            Ключевые возможности
          </h2>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '2rem'
          }}>
            {[
              { icon: '🤖', title: 'ИИ-аналитика', desc: 'Умные алгоритмы для оптимальных цен' },
              { icon: '📈', title: 'Динамические цены', desc: 'Автоматическая корректировка в реальном времени' },
              { icon: '🛡️', title: 'Защита от демпинга', desc: 'Безопасность вашего бизнеса' },
              { icon: '⚙️', title: 'Автоматизация', desc: 'Экономия 20+ часов в неделю' }
            ].map((feature, index) => (
              <div
                key={index}
                style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  padding: '2rem',
                  borderRadius: '20px',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  transition: 'all 0.3s ease'
                }}
              >
                <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>
                  {feature.icon}
                </div>
                <h3 style={{
                  fontSize: '1.3rem',
                  fontWeight: 'bold',
                  marginBottom: '1rem'
                }}>
                  {feature.title}
                </h3>
                <p style={{
                  opacity: '0.9',
                  lineHeight: '1.5'
                }}>
                  {feature.desc}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer style={{
        background: 'rgba(0, 0, 0, 0.2)',
        padding: '2rem',
        textAlign: 'center',
        marginTop: '2rem'
      }}>
        <p style={{ margin: 0, opacity: '0.8' }}>
          © 2024 Ozon Price Optimizer Pro. Все права защищены.
        </p>
      </footer>
    </div>
  );
}
