import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Heading,
  Text,
  Button,
  Badge,
  Flex,
  Spacer,
  useColorModeValue,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  IconButton,
  useToast,
  GridItem
} from '@chakra-ui/react';
import {
  FiSearch,
  FiPlus,
  FiRefreshCw,
  FiTrendingUp,
  FiTrendingDown,
  FiAlertTriangle,
  FiUsers,
  FiEye,
  FiSettings
} from 'react-icons/fi';
import {
  Competitor,
  CompetitorAlert,
  CompetitorAnalytics,
  CompetitorStatus,
  CompetitorType,
  AlertSeverity
} from '../types';
import competitorApi from '../services/competitorApi';
// import CompetitorSearchModal from '../components/competitor/CompetitorSearchModal';

const CompetitorDashboardPage: React.FC = () => {
  // Состояние
  const [competitors, setCompetitors] = useState<Competitor[]>([]);
  const [alerts, setAlerts] = useState<CompetitorAlert[]>([]);
  const [analytics, setAnalytics] = useState<CompetitorAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [isSearchModalOpen, setIsSearchModalOpen] = useState(false);

  // Хуки
  const toast = useToast();

  // Цвета для темной/светлой темы
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // Загрузка данных при монтировании
  useEffect(() => {
    loadDashboardData();
  }, []);

  // Загрузка всех данных дашборда
  const loadDashboardData = async () => {
    try {
      setLoading(true);

      const [competitorsData, alertsData, analyticsData] = await Promise.all([
        competitorApi.getCompetitors(),
        competitorApi.getCompetitorAlerts({ unreadOnly: false }),
        competitorApi.getCompetitorAnalytics('30d')
      ]);

      setCompetitors(competitorsData);
      setAlerts(alertsData);
      setAnalytics(analyticsData);
    } catch (error) {
      toast({
        title: 'Ошибка загрузки',
        description: 'Не удалось загрузить данные дашборда',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // Обновление данных
  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      await competitorApi.triggerManualUpdate();
      await loadDashboardData();

      toast({
        title: 'Данные обновлены',
        description: 'Информация о конкурентах успешно обновлена',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: 'Ошибка обновления',
        description: 'Не удалось обновить данные',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setRefreshing(false);
    }
  };

  // Получение цвета статуса
  const getStatusColor = (status: CompetitorStatus) => {
    switch (status) {
      case CompetitorStatus.ACTIVE: return 'green';
      case CompetitorStatus.SUSPICIOUS: return 'orange';
      case CompetitorStatus.BLOCKED: return 'red';
      case CompetitorStatus.INACTIVE: return 'gray';
      default: return 'gray';
    }
  };

  // Получение цвета типа конкурента
  const getTypeColor = (type: CompetitorType) => {
    switch (type) {
      case CompetitorType.MAIN: return 'blue';
      case CompetitorType.SECONDARY: return 'purple';
      case CompetitorType.AGGRESSIVE: return 'red';
      default: return 'gray';
    }
  };

  // Получение цвета важности алерта
  const getSeverityColor = (severity: AlertSeverity) => {
    switch (severity) {
      case AlertSeverity.LOW: return 'green';
      case AlertSeverity.MEDIUM: return 'yellow';
      case AlertSeverity.HIGH: return 'orange';
      case AlertSeverity.CRITICAL: return 'red';
      default: return 'gray';
    }
  };

  if (loading) {
    return (
      <Box bg={bgColor} minH="100vh" py={8}>
        <Container maxW="7xl">
          <Text>Загрузка дашборда...</Text>
        </Container>
      </Box>
    );
  }

  return (
    <Box bg={bgColor} minH="100vh" py={8}>
      <Container maxW="7xl">
        {/* Заголовок */}
        <Flex mb={8} align="center">
          <Box>
            <Heading size="lg" mb={2}>
              🎯 Управление конкурентами
            </Heading>
            <Text color="gray.600">
              Мониторинг и анализ конкурентов на Ozon
            </Text>
          </Box>
          <Spacer />
          <Flex gap={3}>
            <IconButton
              aria-label="Обновить данные"
              icon={<FiRefreshCw size={20} />}
              onClick={handleRefresh}
              isLoading={refreshing}
              colorScheme="blue"
              variant="outline"
            />
            <Button
              leftIcon={<FiSearch size={20} />}
              colorScheme="blue"
              variant="outline"
              onClick={() => setIsSearchModalOpen(true)}
            >
              Найти конкурентов
            </Button>
            <Button
              leftIcon={<FiPlus size={20} />}
              colorScheme="blue"
              onClick={() => setIsSearchModalOpen(true)}
            >
              Добавить конкурента
            </Button>
          </Flex>
        </Flex>

        {/* Статистика */}
        {analytics && (
          <Grid templateColumns={{ base: '1fr', md: 'repeat(4, 1fr)' }} gap={6} mb={8}>
            <Box bg={cardBg} borderColor={borderColor} borderWidth="1px" borderRadius="lg" p={6}>
              <Stat>
                <StatLabel>Всего конкурентов</StatLabel>
                <StatNumber>{analytics.totalCompetitors}</StatNumber>
                <StatHelpText>
                  <StatArrow type="increase" />
                  Активных: {analytics.activeCompetitors}
                </StatHelpText>
              </Stat>
            </Box>

            <Box bg={cardBg} borderColor={borderColor} borderWidth="1px" borderRadius="lg" p={6}>
              <Stat>
                <StatLabel>Средний рейтинг</StatLabel>
                <StatNumber>{analytics.averageRating.toFixed(1)}</StatNumber>
                <StatHelpText>
                  ⭐ Рейтинг конкурентов
                </StatHelpText>
              </Stat>
            </Box>

            <Box bg={cardBg} borderColor={borderColor} borderWidth="1px" borderRadius="lg" p={6}>
              <Stat>
                <StatLabel>Разница в ценах</StatLabel>
                <StatNumber>
                  {analytics.averagePriceDifference > 0 ? '+' : ''}
                  {analytics.averagePriceDifference.toFixed(1)}%
                </StatNumber>
                <StatHelpText>
                  <StatArrow type={analytics.averagePriceDifference > 0 ? 'increase' : 'decrease'} />
                  От наших цен
                </StatHelpText>
              </Stat>
            </Box>

            <Box bg={cardBg} borderColor={borderColor} borderWidth="1px" borderRadius="lg" p={6}>
              <Stat>
                <StatLabel>Подозрительных</StatLabel>
                <StatNumber color="orange.500">
                  {analytics.suspiciousCompetitors}
                </StatNumber>
                <StatHelpText>
                  <FiAlertTriangle size={16} />
                  Требуют внимания
                </StatHelpText>
              </Stat>
            </Box>
          </Grid>
        )}

        {/* Критичные алерты */}
        {alerts.filter(alert => alert.severity === AlertSeverity.CRITICAL).length > 0 && (
          <Alert status="error" mb={6} borderRadius="lg">
            <AlertIcon />
            <Box>
              <AlertTitle>Критичные алерты!</AlertTitle>
              <AlertDescription>
                Обнаружено {alerts.filter(alert => alert.severity === AlertSeverity.CRITICAL).length} критичных проблем с конкурентами
              </AlertDescription>
            </Box>
          </Alert>
        )}

        <Grid templateColumns={{ base: '1fr', lg: '2fr 1fr' }} gap={8}>
          {/* Список конкурентов */}
          <Box bg={cardBg} borderColor={borderColor} borderWidth="1px" borderRadius="lg" p={6}>
            <Flex mb={4} align="center">
              <Heading size="md">Активные конкуренты ({competitors.length})</Heading>
              <Spacer />
              <Button size="sm" variant="ghost" leftIcon={<FiSettings size={16} />}>
                Настройки
              </Button>
            </Flex>

              {competitors.slice(0, 10).map((competitor) => (
                <Box
                  key={competitor.id}
                  p={4}
                  borderWidth={1}
                  borderColor={borderColor}
                  borderRadius="lg"
                  mb={3}
                  _hover={{ bg: useColorModeValue('gray.50', 'gray.700') }}
                >
                  <Flex align="center" mb={2}>
                    <Text fontWeight="bold" fontSize="lg">
                      {competitor.name}
                    </Text>
                    <Spacer />
                    <Badge colorScheme={getStatusColor(competitor.status)} mr={2}>
                      {competitor.status}
                    </Badge>
                    <Badge colorScheme={getTypeColor(competitor.competitorType)}>
                      {competitor.competitorType}
                    </Badge>
                  </Flex>

                  <Grid templateColumns="repeat(3, 1fr)" gap={4} fontSize="sm">
                    <Box>
                      <Text color="gray.500">Рейтинг</Text>
                      <Text fontWeight="medium">⭐ {competitor.rating}</Text>
                    </Box>
                    <Box>
                      <Text color="gray.500">Товаров</Text>
                      <Text fontWeight="medium">{competitor.totalProducts}</Text>
                    </Box>
                    <Box>
                      <Text color="gray.500">Разница цен</Text>
                      <Text
                        fontWeight="medium"
                        color={competitor.averagePriceDifference > 0 ? 'red.500' : 'green.500'}
                      >
                        {competitor.averagePriceDifference > 0 ? '+' : ''}
                        {competitor.averagePriceDifference.toFixed(1)}%
                      </Text>
                    </Box>
                  </Grid>

                  <Flex mt={3} gap={2}>
                    <Button size="xs" variant="outline" leftIcon={<FiEye size={14} />}>
                      Подробнее
                    </Button>
                    <Button size="xs" variant="outline" leftIcon={<FiTrendingUp size={14} />}>
                      Аналитика
                    </Button>
                  </Flex>
                </Box>
              ))}
          </Box>

          {/* Последние алерты */}
          <Box bg={cardBg} borderColor={borderColor} borderWidth="1px" borderRadius="lg" p={6}>
            <Heading size="md" mb={4}>
              Последние алерты ({alerts.length})
            </Heading>

              {alerts.slice(0, 8).map((alert) => (
                <Box
                  key={alert.id}
                  p={3}
                  borderWidth={1}
                  borderColor={borderColor}
                  borderRadius="md"
                  mb={3}
                  borderLeftWidth={4}
                  borderLeftColor={`${getSeverityColor(alert.severity)}.500`}
                >
                  <Flex align="center" mb={1}>
                    <Badge colorScheme={getSeverityColor(alert.severity)} size="sm">
                      {alert.severity}
                    </Badge>
                    <Spacer />
                    <Text fontSize="xs" color="gray.500">
                      {new Date(alert.createdAt).toLocaleDateString()}
                    </Text>
                  </Flex>

                  <Text fontWeight="medium" fontSize="sm" mb={1}>
                    {alert.title}
                  </Text>

                  <Text fontSize="xs" color="gray.600" noOfLines={2}>
                    {alert.description}
                  </Text>
                </Box>
              ))}
          </Box>
        </Grid>

        {/* Модал поиска конкурентов */}
        {/* <CompetitorSearchModal
          isOpen={isSearchModalOpen}
          onClose={() => setIsSearchModalOpen(false)}
          onCompetitorAdded={loadDashboardData}
        /> */}
      </Container>
    </Box>
  );
};

export default CompetitorDashboardPage;
