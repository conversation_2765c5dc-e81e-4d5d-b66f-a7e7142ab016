import React from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  Flex,
  Spacer,
  useColorModeValue,
  useColorMode,
  Grid,
  Badge,
  Icon,
  VStack,
  HStack,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Progress,
  Divider,
  IconButton
} from '@chakra-ui/react';
import {
  FiSearch,
  FiPlus,
  FiTrendingUp,
  FiUsers,
  FiShield,
  FiTarget,
  FiBarChart2,
  FiAlertTriangle,
  FiCheckCircle,
  FiClock,
  FiSun,
  FiMoon
} from 'react-icons/fi';

const CompetitorDashboardPageSimple: React.FC = () => {
  // Хуки
  const { colorMode, toggleColorMode } = useColorMode();

  // Цвета из брендбука
  const bgColor = useColorModeValue('#F9FAFB', '#0F172A'); // Secondary background
  const cardBg = useColorModeValue('#FFFFFF', '#1E293B'); // Primary background
  const borderColor = useColorModeValue('#E5E7EB', '#475569'); // Border colors
  const primaryBlue = useColorModeValue('#2B5CE6', '#3B82F6'); // Primary Blue
  const textPrimary = useColorModeValue('#1A1D29', '#F8FAFC'); // Primary text
  const textSecondary = useColorModeValue('#374151', '#CBD5E1'); // Secondary text
  const textTertiary = useColorModeValue('#6B7280', '#94A3B8'); // Tertiary text

  return (
    <Box bg={bgColor} minH="100vh" py={8}>
      <Container maxW="7xl">
        {/* Навигация */}
        <Flex mb={6} align="center" justify="space-between">
          <Flex align="center" gap={4}>
            <Button
              as="a"
              href="/"
              variant="ghost"
              size="sm"
              color={textSecondary}
              fontFamily="Inter"
            >
              ← Главная
            </Button>
            <Text color={textTertiary} fontSize="sm">|</Text>
            <Text color={primaryBlue} fontSize="sm" fontWeight="medium" fontFamily="Inter">
              Управление конкурентами
            </Text>
          </Flex>

          {/* Переключатель темы */}
          <IconButton
            aria-label="Переключить тему"
            icon={colorMode === 'light' ? <FiMoon /> : <FiSun />}
            onClick={toggleColorMode}
            variant="ghost"
            size="sm"
            color={textSecondary}
          />
        </Flex>
        {/* Заголовок в стиле брендбука */}
        <Flex mb={8} align="center">
          <Box>
            <Heading
              fontSize="32px"
              lineHeight="40px"
              fontWeight="bold"
              mb={2}
              color={textPrimary}
              fontFamily="Inter"
            >
              🎯 Управление конкурентами
            </Heading>
            <Text
              fontSize="16px"
              lineHeight="24px"
              color={textSecondary}
              fontFamily="Inter"
            >
              Мониторинг и анализ конкурентов на Ozon
            </Text>
          </Box>
          <Spacer />
          <Flex gap={3}>
            <Button
              leftIcon={<FiSearch size={20} />}
              bg="transparent"
              color={primaryBlue}
              border="1px solid"
              borderColor={primaryBlue}
              borderRadius="8px"
              px="24px"
              py="12px"
              fontSize="14px"
              fontWeight="semibold"
              fontFamily="Inter"
              _hover={{ bg: useColorModeValue('#EEF2FF', 'rgba(59, 130, 246, 0.1)') }}
              _active={{ bg: useColorModeValue('#E0E7FF', 'rgba(59, 130, 246, 0.2)') }}
            >
              Найти конкурентов
            </Button>
            <Button
              leftIcon={<FiPlus size={20} />}
              bg={primaryBlue}
              color="white"
              borderRadius="8px"
              px="24px"
              py="12px"
              fontSize="14px"
              fontWeight="semibold"
              fontFamily="Inter"
              _hover={{ bg: useColorModeValue('#1E3BA3', '#2563EB') }}
              _active={{ bg: useColorModeValue('#1E40AF', '#1D4ED8') }}
            >
              Добавить конкурента
            </Button>
          </Flex>
        </Flex>

        {/* Статистика в стиле брендбука */}
        <Grid templateColumns={{ base: '1fr', md: 'repeat(4, 1fr)' }} gap={6} mb={8}>
          <Box
            bg={cardBg}
            borderColor={borderColor}
            borderWidth="1px"
            borderRadius="12px"
            p={6}
            boxShadow="0 1px 3px rgba(0,0,0,0.1)"
          >
            <Stat>
              <StatLabel color={textTertiary} fontSize="12px" fontFamily="Inter">Всего конкурентов</StatLabel>
              <StatNumber color={primaryBlue} fontSize="24px" fontWeight="semibold" fontFamily="Inter">24</StatNumber>
              <StatHelpText color={textSecondary} fontSize="12px" fontFamily="Inter">
                <StatArrow type="increase" />
                Активных: 18
              </StatHelpText>
            </Stat>
          </Box>

          <Box
            bg={cardBg}
            borderColor={borderColor}
            borderWidth="1px"
            borderRadius="12px"
            p={6}
            boxShadow="0 1px 3px rgba(0,0,0,0.1)"
          >
            <Stat>
              <StatLabel color={textTertiary} fontSize="12px" fontFamily="Inter">Средний рейтинг</StatLabel>
              <StatNumber color="#00C851" fontSize="24px" fontWeight="semibold" fontFamily="Inter">4.2</StatNumber>
              <StatHelpText color={textSecondary} fontSize="12px" fontFamily="Inter">
                ⭐ Рейтинг конкурентов
              </StatHelpText>
            </Stat>
          </Box>

          <Box
            bg={cardBg}
            borderColor={borderColor}
            borderWidth="1px"
            borderRadius="12px"
            p={6}
            boxShadow="0 1px 3px rgba(0,0,0,0.1)"
          >
            <Stat>
              <StatLabel color={textTertiary} fontSize="12px" fontFamily="Inter">Разница в ценах</StatLabel>
              <StatNumber color="#FF6B35" fontSize="24px" fontWeight="semibold" fontFamily="Inter">+12.5%</StatNumber>
              <StatHelpText color={textSecondary} fontSize="12px" fontFamily="Inter">
                <StatArrow type="increase" />
                От наших цен
              </StatHelpText>
            </Stat>
          </Box>

          <Box
            bg={cardBg}
            borderColor={borderColor}
            borderWidth="1px"
            borderRadius="12px"
            p={6}
            boxShadow="0 1px 3px rgba(0,0,0,0.1)"
          >
            <Stat>
              <StatLabel color={textTertiary} fontSize="12px" fontFamily="Inter">Подозрительных</StatLabel>
              <StatNumber color="#FF3547" fontSize="24px" fontWeight="semibold" fontFamily="Inter">3</StatNumber>
              <StatHelpText color={textSecondary} fontSize="12px" fontFamily="Inter">
                <FiAlertTriangle size={16} />
                Требуют внимания
              </StatHelpText>
            </Stat>
          </Box>
        </Grid>

        <Grid templateColumns={{ base: '1fr', lg: '2fr 1fr' }} gap={8}>
          {/* Основной контент */}
          <VStack spacing={6} align="stretch">
            {/* Статус системы в стиле брендбука */}
            <Box
              bg={cardBg}
              borderColor={borderColor}
              borderWidth="1px"
              borderRadius="12px"
              p={6}
              boxShadow="0 1px 3px rgba(0,0,0,0.1)"
            >
              <Heading
                fontSize="18px"
                lineHeight="24px"
                fontWeight="semibold"
                mb={4}
                color={textPrimary}
                fontFamily="Inter"
              >
                🎉 Система управления конкурентами работает!
              </Heading>
              <Text
                mb={4}
                color={textSecondary}
                fontSize="16px"
                lineHeight="24px"
                fontFamily="Inter"
              >
                Добро пожаловать в центр управления конкурентами Ozon Price Optimizer Pro
              </Text>

              <VStack spacing={4} align="stretch">
                <HStack>
                  <Icon as={FiCheckCircle} color="#00C851" />
                  <Text color={textPrimary} fontSize="14px" fontFamily="Inter">🔍 Поиск и добавление конкурентов</Text>
                </HStack>
                <HStack>
                  <Icon as={FiCheckCircle} color="#00C851" />
                  <Text color={textPrimary} fontSize="14px" fontFamily="Inter">📊 Мониторинг цен и активности</Text>
                </HStack>
                <HStack>
                  <Icon as={FiCheckCircle} color="#00C851" />
                  <Text color={textPrimary} fontSize="14px" fontFamily="Inter">🤖 ИИ-анализ конкурентной среды</Text>
                </HStack>
                <HStack>
                  <Icon as={FiCheckCircle} color="#00C851" />
                  <Text color={textPrimary} fontSize="14px" fontFamily="Inter">🚨 Алерты о подозрительной активности</Text>
                </HStack>
                <HStack>
                  <Icon as={FiCheckCircle} color="#00C851" />
                  <Text color={textPrimary} fontSize="14px" fontFamily="Inter">🛡️ Детектор демпинга и махинаций</Text>
                </HStack>
              </VStack>
            </Box>

            {/* Прогресс настройки в стиле брендбука */}
            <Box
              bg={cardBg}
              borderColor={borderColor}
              borderWidth="1px"
              borderRadius="12px"
              p={6}
              boxShadow="0 1px 3px rgba(0,0,0,0.1)"
            >
              <Heading
                fontSize="18px"
                lineHeight="24px"
                fontWeight="semibold"
                mb={4}
                color={textPrimary}
                fontFamily="Inter"
              >
                📈 Прогресс настройки системы
              </Heading>

              <VStack spacing={4} align="stretch">
                <Box>
                  <Flex justify="space-between" mb={2}>
                    <Text fontSize="14px" color={textPrimary} fontFamily="Inter">Базовая настройка</Text>
                    <Badge
                      bg="#DCFCE7"
                      color="#15803D"
                      fontSize="11px"
                      fontWeight="medium"
                      px={2}
                      py={1}
                      borderRadius="4px"
                    >
                      Завершено
                    </Badge>
                  </Flex>
                  <Progress value={100} bg={useColorModeValue('#F3F4F6', '#334155')} sx={{
                    '& > div': {
                      bg: '#00C851'
                    }
                  }} />
                </Box>

                <Box>
                  <Flex justify="space-between" mb={2}>
                    <Text fontSize="14px" color={textPrimary} fontFamily="Inter">Добавление конкурентов</Text>
                    <Badge
                      bg="#FEF3C7"
                      color="#92400E"
                      fontSize="11px"
                      fontWeight="medium"
                      px={2}
                      py={1}
                      borderRadius="4px"
                    >
                      В процессе
                    </Badge>
                  </Flex>
                  <Progress value={65} bg={useColorModeValue('#F3F4F6', '#334155')} sx={{
                    '& > div': {
                      bg: '#FFB800'
                    }
                  }} />
                </Box>

                <Box>
                  <Flex justify="space-between" mb={2}>
                    <Text fontSize="14px" color={textPrimary} fontFamily="Inter">Настройка алертов</Text>
                    <Badge
                      bg={useColorModeValue('#F3F4F6', '#334155')}
                      color={textTertiary}
                      fontSize="11px"
                      fontWeight="medium"
                      px={2}
                      py={1}
                      borderRadius="4px"
                    >
                      Ожидает
                    </Badge>
                  </Flex>
                  <Progress value={25} bg={useColorModeValue('#F3F4F6', '#334155')} sx={{
                    '& > div': {
                      bg: textTertiary
                    }
                  }} />
                </Box>
              </VStack>
            </Box>
          </VStack>

          {/* Боковая панель в стиле брендбука */}
          <VStack spacing={6} align="stretch">
            {/* Быстрые действия */}
            <Box
              bg={cardBg}
              borderColor={borderColor}
              borderWidth="1px"
              borderRadius="12px"
              p={6}
              boxShadow="0 1px 3px rgba(0,0,0,0.1)"
            >
              <Heading
                fontSize="18px"
                lineHeight="24px"
                fontWeight="semibold"
                mb={4}
                color={textPrimary}
                fontFamily="Inter"
              >
                ⚡ Быстрые действия
              </Heading>

              <VStack spacing={3} align="stretch">
                <Button
                  leftIcon={<FiTarget />}
                  bg="transparent"
                  color={primaryBlue}
                  border="1px solid"
                  borderColor={borderColor}
                  borderRadius="8px"
                  fontSize="14px"
                  fontFamily="Inter"
                  justifyContent="flex-start"
                  _hover={{ bg: useColorModeValue('#F9FAFB', '#334155') }}
                >
                  Анализ конкурентов
                </Button>
                <Button
                  leftIcon={<FiBarChart2 />}
                  bg="transparent"
                  color={primaryBlue}
                  border="1px solid"
                  borderColor={borderColor}
                  borderRadius="8px"
                  fontSize="14px"
                  fontFamily="Inter"
                  justifyContent="flex-start"
                  _hover={{ bg: useColorModeValue('#F9FAFB', '#334155') }}
                >
                  Отчет по ценам
                </Button>
                <Button
                  leftIcon={<FiShield />}
                  bg="transparent"
                  color={primaryBlue}
                  border="1px solid"
                  borderColor={borderColor}
                  borderRadius="8px"
                  fontSize="14px"
                  fontFamily="Inter"
                  justifyContent="flex-start"
                  _hover={{ bg: useColorModeValue('#F9FAFB', '#334155') }}
                >
                  Проверка демпинга
                </Button>
                <Button
                  leftIcon={<FiUsers />}
                  bg="transparent"
                  color={primaryBlue}
                  border="1px solid"
                  borderColor={borderColor}
                  borderRadius="8px"
                  fontSize="14px"
                  fontFamily="Inter"
                  justifyContent="flex-start"
                  _hover={{ bg: useColorModeValue('#F9FAFB', '#334155') }}
                >
                  Топ конкуренты
                </Button>
              </VStack>
            </Box>

            {/* Последние события */}
            <Box
              bg={cardBg}
              borderColor={borderColor}
              borderWidth="1px"
              borderRadius="12px"
              p={6}
              boxShadow="0 1px 3px rgba(0,0,0,0.1)"
            >
              <Heading
                fontSize="18px"
                lineHeight="24px"
                fontWeight="semibold"
                mb={4}
                color={textPrimary}
                fontFamily="Inter"
              >
                📋 Последние события
              </Heading>

              <VStack spacing={3} align="stretch">
                <HStack>
                  <Icon as={FiClock} color={primaryBlue} />
                  <Text fontSize="14px" color={textPrimary} fontFamily="Inter">Система запущена</Text>
                </HStack>
                <HStack>
                  <Icon as={FiCheckCircle} color="#00C851" />
                  <Text fontSize="14px" color={textPrimary} fontFamily="Inter">API подключен</Text>
                </HStack>
                <HStack>
                  <Icon as={FiTrendingUp} color="#FF6B35" />
                  <Text fontSize="14px" color={textPrimary} fontFamily="Inter">Готов к работе</Text>
                </HStack>
              </VStack>
            </Box>
          </VStack>
        </Grid>
      </Container>
    </Box>
  );
};

export default CompetitorDashboardPageSimple;
