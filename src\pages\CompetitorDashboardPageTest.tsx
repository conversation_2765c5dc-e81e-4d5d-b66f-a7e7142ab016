import React from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  Flex,
  useColorModeValue,
  useColorMode,
  IconButton
} from '@chakra-ui/react';
import {
  FiSearch,
  FiPlus,
  FiSun,
  FiMoon
} from 'react-icons/fi';

const CompetitorDashboardPageTest: React.FC = () => {
  const { colorMode, toggleColorMode } = useColorMode();
  
  // Простые цвета
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.800', 'white');

  return (
    <Box bg={bgColor} minH="100vh" py={8}>
      <Container maxW="7xl">
        {/* Навигация */}
        <Flex mb={6} align="center" justify="space-between">
          <Text color={textColor} fontSize="sm">
            ← Главная | Управление конкурентами
          </Text>
          
          <IconButton
            aria-label="Переключить тему"
            icon={colorMode === 'light' ? <FiMoon /> : <FiSun />}
            onClick={toggleColorMode}
            variant="ghost"
            size="sm"
          />
        </Flex>

        {/* Заголовок */}
        <Flex mb={8} align="center">
          <Box>
            <Heading size="lg" mb={2} color={textColor}>
              🎯 Управление конкурентами
            </Heading>
            <Text color="gray.600">
              Мониторинг и анализ конкурентов на Ozon
            </Text>
          </Box>
          <Flex ml="auto" gap={3}>
            <Button
              leftIcon={<FiSearch />}
              colorScheme="blue"
              variant="outline"
            >
              Найти конкурентов
            </Button>
            <Button
              leftIcon={<FiPlus />}
              colorScheme="blue"
            >
              Добавить конкурента
            </Button>
          </Flex>
        </Flex>

        {/* Тестовый контент */}
        <Box bg={cardBg} p={6} borderRadius="lg" boxShadow="sm">
          <Heading size="md" mb={4} color={textColor}>
            ✅ Система управления конкурентами работает!
          </Heading>
          <Text mb={4} color="gray.600">
            Это тестовая версия дашборда конкурентов. Основные функции:
          </Text>
          <Box as="ul" pl={6}>
            <Text as="li" mb={2} color={textColor}>🔍 Поиск и добавление конкурентов</Text>
            <Text as="li" mb={2} color={textColor}>📊 Мониторинг цен и активности</Text>
            <Text as="li" mb={2} color={textColor}>🤖 ИИ-анализ конкурентной среды</Text>
            <Text as="li" mb={2} color={textColor}>🚨 Алерты о подозрительной активности</Text>
            <Text as="li" mb={2} color={textColor}>🛡️ Детектор демпинга и махинаций</Text>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default CompetitorDashboardPageTest;
