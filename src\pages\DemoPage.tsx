import {
  Box,
  Button,
  Container,
  Flex,
  Heading,
  Icon,
  Stack,
  Text,
  SimpleGrid,
  useColorModeValue,
  VStack,
  HStack,
  Badge,
  Progress,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Card,
  CardBody,
  CardHeader,
} from '@chakra-ui/react';
import { 
  FaRocket, 
  FaChartLine, 
  FaShieldAlt, 
  FaCog, 
  FaArrowRight, 
  FaStar, 
  FaCheck,
  FaTrendingUp,
  FaUsers,
  FaGlobe,
} from 'react-icons/fa';
import { Link as RouterLink } from 'react-router-dom';

export default function DemoPage() {
  return (
    <Box>
      {/* Hero Section */}
      <Box
        bgGradient={useColorModeValue(
          'linear(to-br, blue.50, purple.50)',
          'linear(to-br, gray.900, blue.900)'
        )}
        pt={20}
        pb={32}
        position="relative"
        overflow="hidden"
      >
        <Container maxW="container.xl" position="relative" zIndex="1">
          <VStack spacing={8} textAlign="center">
            <Badge
              colorScheme="blue"
              variant="subtle"
              px={4}
              py={2}
              borderRadius="full"
              fontSize="sm"
              fontWeight="semibold"
            >
              🎯 Демо-версия
            </Badge>
            
            <Heading
              as="h1"
              fontSize={{ base: '4xl', md: '6xl' }}
              fontWeight="bold"
              lineHeight="1.1"
              bgGradient="linear(to-r, blue.400, purple.500)"
              bgClip="text"
            >
              Красивый дизайн работает!
            </Heading>
            
            <Text 
              fontSize={{ base: 'lg', md: 'xl' }} 
              color={useColorModeValue('gray.600', 'gray.300')}
              maxW="3xl"
            >
              Это демонстрация современного, красивого и функционального дизайна 
              для Ozon Price Optimizer Pro
            </Text>
            
            <HStack spacing={4}>
              <Button
                size="lg"
                colorScheme="blue"
                px={8}
                rightIcon={<FaArrowRight />}
                _hover={{
                  transform: "translateY(-2px)",
                  boxShadow: "xl"
                }}
              >
                Попробовать сейчас
              </Button>
              
              <Button
                size="lg"
                variant="outline"
                colorScheme="blue"
                px={8}
              >
                Узнать больше
              </Button>
            </HStack>
          </VStack>
        </Container>
      </Box>

      {/* Stats Section */}
      <Container maxW="container.xl" py={20}>
        <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
          <Card>
            <CardBody textAlign="center">
              <VStack spacing={4}>
                <Icon as={FaTrendingUp} w={12} h={12} color="green.500" />
                <Stat>
                  <StatNumber fontSize="3xl" color="green.500">+45%</StatNumber>
                  <StatLabel>Рост прибыли</StatLabel>
                  <StatHelpText>
                    <StatArrow type="increase" />
                    За последний месяц
                  </StatHelpText>
                </Stat>
              </VStack>
            </CardBody>
          </Card>

          <Card>
            <CardBody textAlign="center">
              <VStack spacing={4}>
                <Icon as={FaUsers} w={12} h={12} color="blue.500" />
                <Stat>
                  <StatNumber fontSize="3xl" color="blue.500">1,247</StatNumber>
                  <StatLabel>Активных товаров</StatLabel>
                  <StatHelpText>Под управлением ИИ</StatHelpText>
                </Stat>
              </VStack>
            </CardBody>
          </Card>

          <Card>
            <CardBody textAlign="center">
              <VStack spacing={4}>
                <Icon as={FaGlobe} w={12} h={12} color="purple.500" />
                <Stat>
                  <StatNumber fontSize="3xl" color="purple.500">24/7</StatNumber>
                  <StatLabel>Мониторинг</StatLabel>
                  <StatHelpText>Непрерывная работа</StatHelpText>
                </Stat>
              </VStack>
            </CardBody>
          </Card>
        </SimpleGrid>
      </Container>

      {/* Features Section */}
      <Box bg={useColorModeValue('gray.50', 'gray.800')} py={20}>
        <Container maxW="container.xl">
          <VStack spacing={12}>
            <VStack spacing={4} textAlign="center">
              <Heading as="h2" size="xl">
                Возможности платформы
              </Heading>
              <Text fontSize="lg" color={useColorModeValue('gray.600', 'gray.300')}>
                Все необходимые инструменты для успешных продаж
              </Text>
            </VStack>

            <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={8}>
              {[
                { icon: FaRocket, title: "ИИ-аналитика", desc: "Умные алгоритмы" },
                { icon: FaChartLine, title: "Динамические цены", desc: "Автоматическая корректировка" },
                { icon: FaShieldAlt, title: "Защита от демпинга", desc: "Безопасность бизнеса" },
                { icon: FaCog, title: "Автоматизация", desc: "Экономия времени" },
              ].map((feature, index) => (
                <Card key={index} _hover={{ transform: "translateY(-4px)", boxShadow: "xl" }} transition="all 0.3s">
                  <CardBody>
                    <VStack spacing={4} align="start">
                      <Icon as={feature.icon} w={10} h={10} color="blue.500" />
                      <VStack spacing={2} align="start">
                        <Heading as="h3" size="md">{feature.title}</Heading>
                        <Text color={useColorModeValue('gray.600', 'gray.300')} fontSize="sm">
                          {feature.desc}
                        </Text>
                      </VStack>
                    </VStack>
                  </CardBody>
                </Card>
              ))}
            </SimpleGrid>
          </VStack>
        </Container>
      </Box>

      {/* Progress Section */}
      <Container maxW="container.xl" py={20}>
        <VStack spacing={8}>
          <Heading as="h2" size="xl" textAlign="center">
            Прогресс оптимизации
          </Heading>
          
          <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} w="full">
            <Card>
              <CardHeader>
                <Heading size="md">Анализ конкурентов</Heading>
              </CardHeader>
              <CardBody>
                <VStack spacing={4} align="start">
                  <Text>Обработано товаров: 1,247 из 1,500</Text>
                  <Progress value={83} colorScheme="blue" size="lg" w="full" />
                  <Text fontSize="sm" color="gray.500">83% завершено</Text>
                </VStack>
              </CardBody>
            </Card>

            <Card>
              <CardHeader>
                <Heading size="md">Оптимизация цен</Heading>
              </CardHeader>
              <CardBody>
                <VStack spacing={4} align="start">
                  <Text>Обновлено цен: 956 из 1,247</Text>
                  <Progress value={77} colorScheme="green" size="lg" w="full" />
                  <Text fontSize="sm" color="gray.500">77% завершено</Text>
                </VStack>
              </CardBody>
            </Card>
          </SimpleGrid>
        </VStack>
      </Container>

      {/* CTA Section */}
      <Box bg={useColorModeValue('blue.500', 'blue.600')} py={20} color="white">
        <Container maxW="container.xl">
          <VStack spacing={8} textAlign="center">
            <Heading as="h2" size="xl">
              Готовы начать?
            </Heading>
            <Text fontSize="lg" maxW="2xl">
              Присоединяйтесь к тысячам успешных продавцов, которые уже используют нашу платформу
            </Text>
            <HStack spacing={4}>
              <Button
                as={RouterLink}
                to="/register"
                size="lg"
                bg="white"
                color="blue.500"
                px={8}
                _hover={{ bg: "gray.50" }}
              >
                Начать бесплатно
              </Button>
              <Button
                size="lg"
                variant="outline"
                borderColor="white"
                color="white"
                px={8}
                _hover={{ bg: "whiteAlpha.200" }}
              >
                Связаться с нами
              </Button>
            </HStack>
          </VStack>
        </Container>
      </Box>
    </Box>
  );
}
