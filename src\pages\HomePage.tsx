import {
  Box,
  Button,
  Container,
  Flex,
  Heading,
  Icon,
  Stack,
  Text,
  SimpleGrid,
  useColorModeValue,
  VStack,
  HStack,
  Badge,
} from '@chakra-ui/react';
import {
  FaRobot,
  FaChartLine,
  FaShieldAlt,
  FaCog,
  FaArrowRight,
  FaStar,
  FaCheck,
} from 'react-icons/fa';
import { Link as RouterLink } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

interface FeatureProps {
  icon: React.ElementType;
  title: string;
  description: string;
}

const Feature = ({ icon, title, description }: FeatureProps) => {
  return (
    <Box
      p={6}
      boxShadow="lg"
      borderRadius="xl"
      bg={useColorModeValue('white', 'gray.800')}
      border="1px solid"
      borderColor={useColorModeValue('gray.200', 'gray.700')}
      transition="all 0.3s ease"
      _hover={{
        transform: 'translateY(-4px)',
        boxShadow: 'xl',
        borderColor: useColorModeValue('blue.300', 'blue.500')
      }}
    >
      <VStack align="start" spacing={4}>
        <Flex
          w={12}
          h={12}
          align="center"
          justify="center"
          rounded="lg"
          bg="blue.500"
          color="white"
        >
          <Icon as={icon} w={6} h={6} />
        </Flex>

        <VStack align="start" spacing={2}>
          <Heading as="h3" size="md" fontWeight="bold">
            {title}
          </Heading>
          <Text
            color={useColorModeValue('gray.600', 'gray.300')}
            fontSize="sm"
          >
            {description}
          </Text>
        </VStack>
      </VStack>
    </Box>
  );
};

interface StatBoxProps {
  number: string;
  text: string;
}

function StatBox({ number, text }: StatBoxProps) {
  return (
    <Box
      p={6}
      boxShadow="lg"
      borderRadius="xl"
      bg={useColorModeValue('white', 'gray.800')}
      border="1px solid"
      borderColor={useColorModeValue('gray.200', 'gray.700')}
      transition="all 0.3s ease"
      _hover={{
        transform: 'translateY(-2px)',
        boxShadow: 'xl',
      }}
      textAlign="center"
    >
      <VStack spacing={2}>
        <Text
          fontSize="3xl"
          fontWeight="bold"
          color="blue.500"
        >
          {number}
        </Text>

        <Text
          fontSize="md"
          fontWeight="medium"
          color={useColorModeValue('gray.700', 'gray.200')}
        >
          {text}
        </Text>
      </VStack>
    </Box>
  );
}



export default function HomePage() {
  const { isAuthenticated } = useAuth();

  return (
    <Box>
      {/* Hero Section */}
      <Box
        bg={useColorModeValue('blue.50', 'gray.900')}
        pt={{ base: 16, md: 24 }}
        pb={{ base: 20, md: 32 }}
        position="relative"
        overflow="hidden"
      >
        {/* Background decoration */}
        <Box
          position="absolute"
          top="-10%"
          right="-10%"
          width="400px"
          height="400px"
          bg={useColorModeValue('blue.100', 'blue.800')}
          borderRadius="full"
          opacity="0.3"
          zIndex="0"
        />

        <Container maxW="container.xl" position="relative" zIndex="1">
          <VStack spacing={8} textAlign="center">
            {/* Badge */}
            <Badge
              colorScheme="blue"
              variant="subtle"
              px={4}
              py={2}
              borderRadius="full"
              fontSize="sm"
              fontWeight="semibold"
            >
              🚀 ИИ-платформа для Ozon
            </Badge>

            {/* Main heading */}
            <Heading
              as="h1"
              fontSize={{ base: '4xl', md: '5xl', lg: '6xl' }}
              fontWeight="bold"
              lineHeight="1.1"
              bgGradient="linear(to-r, blue.400, purple.500)"
              bgClip="text"
            >
              Ozon Price Optimizer Pro
            </Heading>

            {/* Subtitle */}
            <Text
              fontSize={{ base: 'lg', md: 'xl' }}
              color={useColorModeValue('gray.600', 'gray.300')}
              maxW="3xl"
              lineHeight="1.6"
            >
              Автоматизированная система ИИ-ценообразования для продавцов Ozon.
              Увеличьте прибыль на 40%, сэкономьте 20 часов в неделю
              и защитите бизнес от недобросовестной конкуренции.
            </Text>

            {/* CTA Buttons */}
            <HStack spacing={4} wrap="wrap" justify="center">
              <Button
                as={RouterLink}
                to={isAuthenticated ? '/ozon-products' : '/register'}
                size="lg"
                colorScheme="blue"
                px={8}
                py={6}
                fontSize="lg"
                fontWeight="bold"
                rightIcon={<FaArrowRight />}
                _hover={{
                  transform: "translateY(-2px)",
                  boxShadow: "xl"
                }}
              >
                {isAuthenticated ? 'Открыть дашборд' : 'Начать бесплатно'}
              </Button>

              <Button
                as={RouterLink}
                to="/demo"
                size="lg"
                variant="outline"
                colorScheme="blue"
                px={8}
                py={6}
                fontSize="lg"
                fontWeight="semibold"
                _hover={{
                  bg: "blue.50",
                  transform: "translateY(-2px)",
                }}
              >
                Посмотреть демо
              </Button>
            </HStack>

            {/* Social proof */}
            <HStack spacing={4} wrap="wrap" justify="center" pt={4}>
              <HStack spacing={1}>
                {Array.from({ length: 5 }).map((_, i) => (
                  <Icon key={i} as={FaStar} color="yellow.400" w={4} h={4} />
                ))}
                <Text fontSize="sm" fontWeight="semibold" ml={2}>
                  4.9/5 рейтинг
                </Text>
              </HStack>

              <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
                500+ довольных клиентов
              </Text>
            </HStack>
          </VStack>
        </Container>
      </Box>

      {/* Features Section */}
      <Container maxW="container.xl" py={20}>
        <VStack spacing={12}>
          <VStack spacing={4} textAlign="center">
            <Heading
              as="h2"
              fontSize={{ base: '3xl', md: '4xl' }}
              fontWeight="bold"
            >
              Ключевые возможности
            </Heading>

            <Text
              fontSize="lg"
              color={useColorModeValue('gray.600', 'gray.300')}
              maxW="2xl"
            >
              Наша платформа объединяет передовые технологии ИИ с глубоким пониманием
              специфики российского e-commerce рынка
            </Text>
          </VStack>

          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={8} w="full">
            <Feature
              icon={FaRobot}
              title="ИИ-аналитика"
              description="Машинное обучение анализирует рынок и предлагает оптимальные цены в реальном времени"
            />
            <Feature
              icon={FaChartLine}
              title="Динамическое ценообразование"
              description="Автоматическая корректировка цен на основе спроса, конкуренции и сезонности"
            />
            <Feature
              icon={FaShieldAlt}
              title="Защита от демпинга"
              description="Интеллектуальная система защиты от недобросовестной конкуренции и ценовых войн"
            />
            <Feature
              icon={FaCog}
              title="Автоматизация"
              description="Полная автоматизация процессов ценообразования с возможностью ручной корректировки"
            />
          </SimpleGrid>
        </VStack>
      </Container>

      {/* Stats Section */}
      <Box bg={useColorModeValue('gray.50', 'gray.800')} py={20}>
        <Container maxW="container.xl">
          <VStack spacing={12}>
            <VStack spacing={4} textAlign="center">
              <Heading
                as="h2"
                fontSize={{ base: '3xl', md: '4xl' }}
                fontWeight="bold"
              >
                Почему выбирают нас
              </Heading>

              <Text
                fontSize="lg"
                color={useColorModeValue('gray.600', 'gray.300')}
                maxW="2xl"
              >
                Более 500 продавцов уже увеличили свою прибыль с помощью нашей платформы
              </Text>
            </VStack>

            <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8} w="full">
              <StatBox number="40%" text="Средний рост прибыли" />
              <StatBox number="500+" text="Довольных клиентов" />
              <StatBox number="24/7" text="Автоматический мониторинг" />
            </SimpleGrid>
          </VStack>
        </Container>
      </Box>

      {/* CTA Section */}
      <Container maxW="container.xl" py={20}>
        <Box
          bg={useColorModeValue('blue.50', 'blue.900')}
          p={10}
          borderRadius="xl"
          boxShadow="lg"
          textAlign="center"
        >
          <VStack spacing={6}>
            <Heading as="h3" size="lg" mb={4}>
              Готовы увеличить прибыль вашего бизнеса на Ozon?
            </Heading>

            <Text fontSize="lg" color={useColorModeValue('gray.600', 'gray.300')} maxW="2xl">
              Присоединяйтесь к сотням продавцов, которые уже оптимизировали свои цены
              и увеличили продажи с помощью нашей платформы.
            </Text>

            <HStack spacing={4} wrap="wrap" justify="center">
              <Button
                as={RouterLink}
                to={isAuthenticated ? '/ozon-products' : '/register'}
                size="lg"
                colorScheme="blue"
                px={8}
                py={6}
                fontSize="lg"
                fontWeight="bold"
                rightIcon={<FaArrowRight />}
              >
                {isAuthenticated ? 'Перейти к дашборду' : 'Начать бесплатно'}
              </Button>

              <Button
                as={RouterLink}
                to="/demo"
                size="lg"
                variant="outline"
                colorScheme="blue"
                px={8}
                py={6}
                fontSize="lg"
              >
                Посмотреть демо
              </Button>
            </HStack>

            <HStack spacing={6} wrap="wrap" justify="center" pt={4}>
              <HStack spacing={2}>
                <Icon as={FaCheck} color="green.500" />
                <Text fontSize="sm">Бесплатный пробный период</Text>
              </HStack>
              <HStack spacing={2}>
                <Icon as={FaCheck} color="green.500" />
                <Text fontSize="sm">Настройка за 5 минут</Text>
              </HStack>
              <HStack spacing={2}>
                <Icon as={FaCheck} color="green.500" />
                <Text fontSize="sm">Поддержка 24/7</Text>
              </HStack>
            </HStack>
          </VStack>
        </Box>
      </Container>
    </Box>
  );
}
