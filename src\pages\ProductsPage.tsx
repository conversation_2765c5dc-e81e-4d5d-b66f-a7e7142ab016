import { useEffect, useState } from 'react';
import {
  Box,
  Container,
  Flex,
  Grid,
  Heading,
  Text,
  Button,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  Spinner,
  Alert,
  AlertIcon,
  useBreakpointValue
} from '@chakra-ui/react';
import { SearchIcon } from '@chakra-ui/icons';
import ProductCard from '../components/ProductCard';
import ProductFilters from '../components/ProductFilters';
import { useProducts } from '../context/ProductContext';
import { Product } from '../types';

export default function ProductsPage() {
  const { products, filteredProducts, isLoading, error, fetchProducts } = useProducts();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [displayProducts, setDisplayProducts] = useState<Product[]>([]);
  
  // Получаем количество колонок в зависимости от размера экрана
  const columnCount = useBreakpointValue({ base: 1, md: 2, lg: 3, xl: 4 });
  
  // Загружаем товары при монтировании компонента
  useEffect(() => {
    fetchProducts();
  }, []);
  
  // Фильтруем и сортируем товары при изменении фильтров, поиска или сортировки
  useEffect(() => {
    let result = [...filteredProducts];
    
    // Применяем поиск
    if (searchTerm) {
      const lowerSearchTerm = searchTerm.toLowerCase();
      result = result.filter(product => 
        product.name.toLowerCase().includes(lowerSearchTerm) ||
        product.sku.toLowerCase().includes(lowerSearchTerm) ||
        product.ozonId.toLowerCase().includes(lowerSearchTerm) ||
        product.brand.toLowerCase().includes(lowerSearchTerm) ||
        product.category.toLowerCase().includes(lowerSearchTerm)
      );
    }
    
    // Применяем сортировку
    result.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'price_asc':
          return a.currentPrice - b.currentPrice;
        case 'price_desc':
          return b.currentPrice - a.currentPrice;
        case 'stock_asc':
          return a.stock - b.stock;
        case 'stock_desc':
          return b.stock - a.stock;
        case 'margin_asc':
          const marginA = (a.currentPrice - a.costPrice) / a.currentPrice;
          const marginB = (b.currentPrice - b.costPrice) / b.currentPrice;
          return marginA - marginB;
        case 'margin_desc':
          const marginADesc = (a.currentPrice - a.costPrice) / a.currentPrice;
          const marginBDesc = (b.currentPrice - b.costPrice) / b.currentPrice;
          return marginBDesc - marginADesc;
        default:
          return 0;
      }
    });
    
    setDisplayProducts(result);
  }, [filteredProducts, searchTerm, sortBy]);
  
  // Обработчик обновления товаров
  const handleRefresh = () => {
    fetchProducts();
  };
  
  return (
    <Container maxW="container.xl" py={8}>
      <Heading as="h1" mb={6}>Управление товарами</Heading>
      
      <Flex 
        direction={{ base: 'column', lg: 'row' }} 
        gap={6}
      >
        {/* Фильтры */}
        <Box 
          width={{ base: '100%', lg: '300px' }} 
          flexShrink={0}
        >
          <ProductFilters onApplyFilters={() => {}} />
        </Box>
        
        {/* Список товаров */}
        <Box flex="1">
          <Flex 
            direction={{ base: 'column', md: 'row' }} 
            justify="space-between" 
            align={{ base: 'stretch', md: 'center' }} 
            mb={4}
            gap={4}
          >
            <InputGroup maxW={{ base: '100%', md: '300px' }}>
              <InputLeftElement pointerEvents="none">
                <SearchIcon color="gray.300" />
              </InputLeftElement>
              <Input 
                placeholder="Поиск товаров..." 
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </InputGroup>
            
            <Flex gap={4}>
              <Select 
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                width={{ base: '100%', md: '200px' }}
              >
                <option value="name">По названию</option>
                <option value="price_asc">Цена (по возрастанию)</option>
                <option value="price_desc">Цена (по убыванию)</option>
                <option value="stock_asc">Остаток (по возрастанию)</option>
                <option value="stock_desc">Остаток (по убыванию)</option>
                <option value="margin_asc">Маржа (по возрастанию)</option>
                <option value="margin_desc">Маржа (по убыванию)</option>
              </Select>
              
              <Button
                colorScheme="blue"
                onClick={handleRefresh}
                isLoading={isLoading}
              >
                Обновить
              </Button>
            </Flex>
          </Flex>
          
          <Flex justify="space-between" align="center" mb={4}>
            <Text fontSize="lg" fontWeight="medium">
              {isLoading 
                ? 'Загрузка товаров...' 
                : `Найдено товаров: ${displayProducts.length} из ${products.length}`}
            </Text>
            <Button
              colorScheme="green"
              size="sm"
            >
              Добавить товар
            </Button>
          </Flex>
          
          {isLoading ? (
            <Flex justify="center" align="center" height="300px">
              <Spinner size="xl" color="blue.500" />
            </Flex>
          ) : error ? (
            <Alert status="error" borderRadius="md">
              <AlertIcon />
              <Text>{error}</Text>
            </Alert>
          ) : displayProducts.length === 0 ? (
            <Alert status="info" borderRadius="md">
              <AlertIcon />
              <Text>
                По вашему запросу не найдено товаров. Попробуйте изменить параметры фильтрации или поиска.
              </Text>
            </Alert>
          ) : (
            <Grid 
              templateColumns={`repeat(${columnCount}, 1fr)`} 
              gap={4}
            >
              {displayProducts.map(product => (
                <ProductCard key={product.id} product={product} />
              ))}
            </Grid>
          )}
        </Box>
      </Flex>
    </Container>
  );
}
