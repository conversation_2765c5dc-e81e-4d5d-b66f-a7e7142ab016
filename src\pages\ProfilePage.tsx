import {
  Box,
  Button,
  Container,
  Divider,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Stack,
  Switch,
  Text,
  useColorModeValue,
  useToast,
  VStack,
  HStack,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  Select,
  Checkbox,
  CheckboxGroup,
} from '@chakra-ui/react';
import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useSlots } from '../context/SlotContext';
import { NotificationPreferences, User } from '../types';

export default function ProfilePage() {
  const { user, updateUser } = useAuth();
  const { warehouses } = useSlots();
  const toast = useToast();
  
  const [name, setName] = useState(user?.name || '');
  const [apiKey, setApiKey] = useState(user?.apiKey || '');
  const [notificationPreferences, setNotificationPreferences] = useState<NotificationPreferences>(
    user?.notificationPreferences || {
      email: true,
      push: true,
      telegram: false,
      telegramChatId: '',
      priorityWarehouses: [],
      priorityTimeSlots: []
    }
  );
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  useEffect(() => {
    if (user) {
      setName(user.name || '');
      setApiKey(user.apiKey || '');
      setNotificationPreferences(user.notificationPreferences);
    }
  }, [user]);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      const updatedUser: Partial<User> = {
        name,
        apiKey,
        notificationPreferences
      };
      
      updateUser(updatedUser);
      
      toast({
        title: 'Профиль обновлен',
        description: 'Ваши настройки успешно сохранены',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: 'Ошибка',
        description: 'Не удалось обновить профиль',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleNotificationChange = (field: keyof NotificationPreferences, value: any) => {
    setNotificationPreferences(prev => ({
      ...prev,
      [field]: value
    }));
  };
  
  const handleWarehouseChange = (selectedWarehouses: string[]) => {
    setNotificationPreferences(prev => ({
      ...prev,
      priorityWarehouses: selectedWarehouses
    }));
  };
  
  return (
    <Container maxW="container.md" py={8}>
      <Heading as="h1" mb={6}>Профиль пользователя</Heading>
      
      <Tabs colorScheme="blue" isLazy>
        <TabList>
          <Tab>Основная информация</Tab>
          <Tab>Настройки уведомлений</Tab>
          <Tab>API Ozon</Tab>
        </TabList>
        
        <TabPanels>
          {/* Основная информация */}
          <TabPanel>
            <Box
              bg={useColorModeValue('white', 'gray.700')}
              p={6}
              borderRadius="md"
              boxShadow="sm"
            >
              <form onSubmit={handleSubmit}>
                <VStack spacing={4} align="stretch">
                  <FormControl>
                    <FormLabel>Email</FormLabel>
                    <Input value={user?.email} isReadOnly />
                  </FormControl>
                  
                  <FormControl>
                    <FormLabel>Имя</FormLabel>
                    <Input 
                      value={name} 
                      onChange={(e) => setName(e.target.value)} 
                      placeholder="Ваше имя"
                    />
                  </FormControl>
                  
                  <Button 
                    colorScheme="blue" 
                    type="submit"
                    isLoading={isSubmitting}
                    alignSelf="flex-start"
                    mt={4}
                  >
                    Сохранить изменения
                  </Button>
                </VStack>
              </form>
            </Box>
          </TabPanel>
          
          {/* Настройки уведомлений */}
          <TabPanel>
            <Box
              bg={useColorModeValue('white', 'gray.700')}
              p={6}
              borderRadius="md"
              boxShadow="sm"
            >
              <form onSubmit={handleSubmit}>
                <VStack spacing={6} align="stretch">
                  <Heading size="md">Каналы уведомлений</Heading>
                  
                  <FormControl display="flex" alignItems="center">
                    <FormLabel htmlFor="email-notifications" mb="0">
                      Email-уведомления
                    </FormLabel>
                    <Switch 
                      id="email-notifications" 
                      isChecked={notificationPreferences.email}
                      onChange={(e) => handleNotificationChange('email', e.target.checked)}
                      colorScheme="blue"
                    />
                  </FormControl>
                  
                  <FormControl display="flex" alignItems="center">
                    <FormLabel htmlFor="push-notifications" mb="0">
                      Push-уведомления
                    </FormLabel>
                    <Switch 
                      id="push-notifications" 
                      isChecked={notificationPreferences.push}
                      onChange={(e) => handleNotificationChange('push', e.target.checked)}
                      colorScheme="blue"
                    />
                  </FormControl>
                  
                  <FormControl display="flex" alignItems="center">
                    <FormLabel htmlFor="telegram-notifications" mb="0">
                      Telegram-уведомления
                    </FormLabel>
                    <Switch 
                      id="telegram-notifications" 
                      isChecked={notificationPreferences.telegram}
                      onChange={(e) => handleNotificationChange('telegram', e.target.checked)}
                      colorScheme="blue"
                    />
                  </FormControl>
                  
                  {notificationPreferences.telegram && (
                    <FormControl>
                      <FormLabel>Telegram Chat ID</FormLabel>
                      <Input 
                        value={notificationPreferences.telegramChatId || ''} 
                        onChange={(e) => handleNotificationChange('telegramChatId', e.target.value)}
                        placeholder="Ваш Chat ID в Telegram"
                      />
                      <Text fontSize="sm" color="gray.500" mt={1}>
                        Чтобы получить Chat ID, напишите боту @userinfobot в Telegram
                      </Text>
                    </FormControl>
                  )}
                  
                  <Divider />
                  
                  <Heading size="md">Приоритетные склады</Heading>
                  <Text fontSize="sm" color="gray.500">
                    Выберите склады, для которых вы хотите получать уведомления в первую очередь
                  </Text>
                  
                  <CheckboxGroup 
                    colorScheme="blue" 
                    value={notificationPreferences.priorityWarehouses || []}
                    onChange={handleWarehouseChange}
                  >
                    <Stack spacing={2}>
                      {warehouses.map(warehouse => (
                        <Checkbox key={warehouse.id} value={warehouse.id}>
                          {warehouse.name}
                        </Checkbox>
                      ))}
                    </Stack>
                  </CheckboxGroup>
                  
                  <Button 
                    colorScheme="blue" 
                    type="submit"
                    isLoading={isSubmitting}
                    alignSelf="flex-start"
                    mt={4}
                  >
                    Сохранить настройки
                  </Button>
                </VStack>
              </form>
            </Box>
          </TabPanel>
          
          {/* API Ozon */}
          <TabPanel>
            <Box
              bg={useColorModeValue('white', 'gray.700')}
              p={6}
              borderRadius="md"
              boxShadow="sm"
            >
              <form onSubmit={handleSubmit}>
                <VStack spacing={4} align="stretch">
                  <Text>
                    Для автоматического бронирования слотов необходимо указать API-ключ Ozon.
                  </Text>
                  
                  <FormControl>
                    <FormLabel>API-ключ Ozon</FormLabel>
                    <Input 
                      value={apiKey} 
                      onChange={(e) => setApiKey(e.target.value)} 
                      placeholder="Ваш API-ключ Ozon"
                      type="password"
                    />
                    <Text fontSize="sm" color="gray.500" mt={1}>
                      API-ключ можно получить в личном кабинете Ozon в разделе "Настройки API"
                    </Text>
                  </FormControl>
                  
                  <Button 
                    colorScheme="blue" 
                    type="submit"
                    isLoading={isSubmitting}
                    alignSelf="flex-start"
                    mt={4}
                  >
                    Сохранить API-ключ
                  </Button>
                </VStack>
              </form>
            </Box>
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Container>
  );
}
