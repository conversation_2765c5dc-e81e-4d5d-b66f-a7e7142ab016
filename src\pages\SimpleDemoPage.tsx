import React from 'react';
import { Link } from 'react-router-dom';

export default function SimpleDemoPage() {
  return (
    <div style={{ 
      minHeight: '100vh', 
      background: 'linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%)',
      color: 'white',
      fontFamily: 'Arial, sans-serif'
    }}>
      {/* Header */}
      <header style={{
        padding: '1rem 2rem',
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(10px)',
        borderBottom: '1px solid rgba(255, 255, 255, 0.2)'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          maxWidth: '1200px',
          margin: '0 auto'
        }}>
          <Link to="/" style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            textDecoration: 'none',
            color: 'white'
          }}>
            <div style={{
              width: '40px',
              height: '40px',
              background: 'linear-gradient(45deg, #ff6b6b, #4ecdc4)',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '20px'
            }}>
              🚀
            </div>
            <h1 style={{ margin: 0, fontSize: '1.5rem', fontWeight: 'bold' }}>
              Ozon Price Optimizer Pro
            </h1>
          </Link>
          
          <nav style={{ display: 'flex', gap: '1rem' }}>
            <Link 
              to="/" 
              style={{
                color: 'white',
                textDecoration: 'none',
                padding: '0.5rem 1rem',
                border: '2px solid white',
                borderRadius: '8px',
                transition: 'all 0.3s ease'
              }}
            >
              Главная
            </Link>
          </nav>
        </div>
      </header>

      {/* Demo Content */}
      <main style={{ padding: '4rem 2rem' }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto'
        }}>
          {/* Hero */}
          <div style={{ textAlign: 'center', marginBottom: '4rem' }}>
            <div style={{
              background: 'rgba(255, 255, 255, 0.1)',
              padding: '0.5rem 1rem',
              borderRadius: '20px',
              display: 'inline-block',
              marginBottom: '2rem',
              fontSize: '0.9rem'
            }}>
              🎯 Демо-версия платформы
            </div>
            
            <h1 style={{
              fontSize: 'clamp(2rem, 4vw, 3rem)',
              fontWeight: 'bold',
              margin: '0 0 1rem 0'
            }}>
              Интерактивная демонстрация
            </h1>
            
            <p style={{
              fontSize: '1.1rem',
              opacity: '0.9',
              maxWidth: '600px',
              margin: '0 auto'
            }}>
              Посмотрите, как работает наша платформа в реальном времени
            </p>
          </div>

          {/* Stats Dashboard */}
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            borderRadius: '20px',
            padding: '2rem',
            marginBottom: '3rem'
          }}>
            <h2 style={{
              fontSize: '1.5rem',
              fontWeight: 'bold',
              marginBottom: '2rem',
              textAlign: 'center'
            }}>
              📊 Панель управления
            </h2>
            
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '1.5rem'
            }}>
              {[
                { title: 'Общая прибыль', value: '₽2,847,392', change: '+32%', color: '#10b981' },
                { title: 'Активных товаров', value: '1,247', change: '+15', color: '#3b82f6' },
                { title: 'Средняя маржа', value: '24.8%', change: '+5.2%', color: '#8b5cf6' },
                { title: 'Экономия времени', value: '23ч/нед', change: 'Автомат', color: '#f59e0b' }
              ].map((stat, index) => (
                <div 
                  key={index}
                  style={{
                    background: 'rgba(255, 255, 255, 0.1)',
                    padding: '1.5rem',
                    borderRadius: '12px',
                    textAlign: 'center'
                  }}
                >
                  <div style={{
                    fontSize: '2rem',
                    fontWeight: 'bold',
                    color: stat.color,
                    marginBottom: '0.5rem'
                  }}>
                    {stat.value}
                  </div>
                  <div style={{
                    fontSize: '0.9rem',
                    opacity: '0.8',
                    marginBottom: '0.5rem'
                  }}>
                    {stat.title}
                  </div>
                  <div style={{
                    fontSize: '0.8rem',
                    color: stat.color,
                    fontWeight: 'bold'
                  }}>
                    {stat.change}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Progress Bars */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '2rem',
            marginBottom: '3rem'
          }}>
            {[
              { title: 'Анализ конкурентов', progress: 85, total: '1,247 товаров' },
              { title: 'Оптимизация цен', progress: 72, total: '956 обновлений' },
              { title: 'Мониторинг изменений', progress: 100, total: '24/7 активно' }
            ].map((item, index) => (
              <div 
                key={index}
                style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  padding: '2rem',
                  borderRadius: '16px'
                }}
              >
                <h3 style={{
                  fontSize: '1.2rem',
                  fontWeight: 'bold',
                  marginBottom: '1rem'
                }}>
                  {item.title}
                </h3>
                
                <div style={{
                  background: 'rgba(255, 255, 255, 0.2)',
                  borderRadius: '10px',
                  height: '10px',
                  marginBottom: '1rem',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    background: 'linear-gradient(90deg, #4ecdc4, #44a08d)',
                    height: '100%',
                    width: `${item.progress}%`,
                    borderRadius: '10px',
                    transition: 'width 2s ease'
                  }} />
                </div>
                
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  fontSize: '0.9rem',
                  opacity: '0.8'
                }}>
                  <span>{item.total}</span>
                  <span>{item.progress}%</span>
                </div>
              </div>
            ))}
          </div>

          {/* Features Grid */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '2rem',
            marginBottom: '3rem'
          }}>
            {[
              { 
                icon: '🤖', 
                title: 'ИИ-анализ', 
                desc: 'Обработано 50,000+ товаров',
                status: 'Активно'
              },
              { 
                icon: '📈', 
                title: 'Динамические цены', 
                desc: 'Обновления каждые 15 минут',
                status: 'Работает'
              },
              { 
                icon: '🛡️', 
                title: 'Защита от демпинга', 
                desc: 'Заблокировано 23 атаки',
                status: 'Защищено'
              },
              { 
                icon: '⚙️', 
                title: 'Автоматизация', 
                desc: 'Сэкономлено 156 часов',
                status: 'Включено'
              }
            ].map((feature, index) => (
              <div 
                key={index}
                style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  padding: '2rem',
                  borderRadius: '16px',
                  position: 'relative',
                  overflow: 'hidden'
                }}
              >
                <div style={{
                  position: 'absolute',
                  top: '1rem',
                  right: '1rem',
                  background: '#10b981',
                  color: 'white',
                  padding: '0.25rem 0.5rem',
                  borderRadius: '12px',
                  fontSize: '0.7rem',
                  fontWeight: 'bold'
                }}>
                  {feature.status}
                </div>
                
                <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>
                  {feature.icon}
                </div>
                <h3 style={{
                  fontSize: '1.3rem',
                  fontWeight: 'bold',
                  marginBottom: '0.5rem'
                }}>
                  {feature.title}
                </h3>
                <p style={{
                  opacity: '0.9',
                  fontSize: '0.9rem'
                }}>
                  {feature.desc}
                </p>
              </div>
            ))}
          </div>

          {/* CTA */}
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            padding: '3rem',
            borderRadius: '20px',
            textAlign: 'center'
          }}>
            <h2 style={{
              fontSize: '2rem',
              fontWeight: 'bold',
              marginBottom: '1rem'
            }}>
              Готовы начать?
            </h2>
            <p style={{
              fontSize: '1.1rem',
              opacity: '0.9',
              marginBottom: '2rem'
            }}>
              Присоединяйтесь к сотням успешных продавцов
            </p>
            
            <div style={{
              display: 'flex',
              gap: '1rem',
              justifyContent: 'center',
              flexWrap: 'wrap'
            }}>
              <Link 
                to="/register"
                style={{
                  background: 'linear-gradient(45deg, #ff6b6b, #4ecdc4)',
                  color: 'white',
                  textDecoration: 'none',
                  padding: '1rem 2rem',
                  borderRadius: '12px',
                  fontSize: '1.1rem',
                  fontWeight: 'bold',
                  transition: 'all 0.3s ease',
                  display: 'inline-block'
                }}
              >
                Начать бесплатно
              </Link>
              
              <Link 
                to="/"
                style={{
                  background: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  textDecoration: 'none',
                  padding: '1rem 2rem',
                  borderRadius: '12px',
                  fontSize: '1.1rem',
                  fontWeight: 'bold',
                  border: '2px solid rgba(255, 255, 255, 0.3)',
                  transition: 'all 0.3s ease',
                  display: 'inline-block'
                }}
              >
                Вернуться на главную
              </Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
