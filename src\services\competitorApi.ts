// API сервис для работы с конкурентами
import {
  Competitor,
  CompetitorSearchResult,
  CompetitorAlert,
  CompetitorAnalytics,
  CompetitorFilters,
  CompetitorProductExtended,
  CompetitorStatus,
  CompetitorType,
  AlertSeverity,
  CompetitorAlertType
} from '../types';

// Импорт мок-данных для разработки
import { mockCompetitorApi } from './mockCompetitorData';

// Базовый URL API (в реальном проекте из переменных окружения)
const API_BASE_URL = import.meta.env.VITE_API_URL || 'https://api.ozonpriceoptimizer.pro/v1';

// Флаг для использования мок-данных (в разработке)
const USE_MOCK_DATA = import.meta.env.DEV || !import.meta.env.VITE_API_URL;

class CompetitorApiService {
  private apiKey: string | null = null;

  constructor() {
    // Получаем API ключ из localStorage или контекста
    this.apiKey = localStorage.getItem('ozon_api_key');
  }

  // Установка API ключа
  setApiKey(apiKey: string) {
    this.apiKey = apiKey;
    localStorage.setItem('ozon_api_key', apiKey);
  }

  // Базовый метод для API запросов
  private async apiRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;

    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.apiKey}`,
      ...options.headers,
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Network error' }));
      throw new Error(error.message || `HTTP ${response.status}`);
    }

    return response.json();
  }

  // === УПРАВЛЕНИЕ КОНКУРЕНТАМИ ===

  // Получить список конкурентов
  async getCompetitors(filters?: CompetitorFilters): Promise<Competitor[]> {
    if (USE_MOCK_DATA) {
      return mockCompetitorApi.getCompetitors();
    }

    const queryParams = new URLSearchParams();

    if (filters) {
      if (filters.status) queryParams.append('status', filters.status.join(','));
      if (filters.type) queryParams.append('type', filters.type.join(','));
      if (filters.ratingRange?.min) queryParams.append('rating_min', filters.ratingRange.min.toString());
      if (filters.ratingRange?.max) queryParams.append('rating_max', filters.ratingRange.max.toString());
      // Добавляем другие фильтры...
    }

    const endpoint = `/competitors${queryParams.toString() ? `?${queryParams}` : ''}`;
    return this.apiRequest<Competitor[]>(endpoint);
  }

  // Получить конкурента по ID
  async getCompetitor(id: string): Promise<Competitor> {
    return this.apiRequest<Competitor>(`/competitors/${id}`);
  }

  // Добавить нового конкурента
  async addCompetitor(shopId: string, competitorType: CompetitorType = CompetitorType.SECONDARY): Promise<Competitor> {
    if (USE_MOCK_DATA) {
      return mockCompetitorApi.addCompetitor(shopId, competitorType);
    }

    return this.apiRequest<Competitor>('/competitors', {
      method: 'POST',
      body: JSON.stringify({
        shopId,
        competitorType,
        isMonitored: true
      })
    });
  }

  // Обновить конкурента
  async updateCompetitor(id: string, updates: Partial<Competitor>): Promise<Competitor> {
    return this.apiRequest<Competitor>(`/competitors/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(updates)
    });
  }

  // Удалить конкурента
  async deleteCompetitor(id: string): Promise<void> {
    return this.apiRequest<void>(`/competitors/${id}`, {
      method: 'DELETE'
    });
  }

  // === ПОИСК КОНКУРЕНТОВ ===

  // Поиск конкурентов по товару
  async searchCompetitorsByProduct(productName: string, category?: string): Promise<CompetitorSearchResult[]> {
    if (USE_MOCK_DATA) {
      return mockCompetitorApi.searchCompetitorsByProduct(productName);
    }

    const queryParams = new URLSearchParams({
      product: productName
    });

    if (category) queryParams.append('category', category);

    return this.apiRequest<CompetitorSearchResult[]>(`/competitors/search?${queryParams}`);
  }

  // Поиск конкурентов по названию магазина
  async searchCompetitorsByShop(shopName: string): Promise<CompetitorSearchResult[]> {
    if (USE_MOCK_DATA) {
      return mockCompetitorApi.searchCompetitorsByShop(shopName);
    }

    const queryParams = new URLSearchParams({
      shop: shopName
    });

    return this.apiRequest<CompetitorSearchResult[]>(`/competitors/search?${queryParams}`);
  }

  // ИИ-рекомендации конкурентов
  async getAICompetitorRecommendations(productIds?: string[]): Promise<CompetitorSearchResult[]> {
    if (USE_MOCK_DATA) {
      return mockCompetitorApi.getAICompetitorRecommendations();
    }

    const body = productIds ? { productIds } : {};

    return this.apiRequest<CompetitorSearchResult[]>('/competitors/ai-recommendations', {
      method: 'POST',
      body: JSON.stringify(body)
    });
  }

  // === ТОВАРЫ КОНКУРЕНТОВ ===

  // Получить товары конкурента
  async getCompetitorProducts(competitorId: string, productId?: string): Promise<CompetitorProductExtended[]> {
    const queryParams = productId ? `?product_id=${productId}` : '';
    return this.apiRequest<CompetitorProductExtended[]>(`/competitors/${competitorId}/products${queryParams}`);
  }

  // Обновить данные о товарах конкурента
  async refreshCompetitorProducts(competitorId: string): Promise<void> {
    return this.apiRequest<void>(`/competitors/${competitorId}/refresh`, {
      method: 'POST'
    });
  }

  // === АЛЕРТЫ ===

  // Получить алерты по конкурентам
  async getCompetitorAlerts(filters?: {
    competitorId?: string;
    severity?: AlertSeverity[];
    type?: CompetitorAlertType[];
    unreadOnly?: boolean;
  }): Promise<CompetitorAlert[]> {
    if (USE_MOCK_DATA) {
      return mockCompetitorApi.getCompetitorAlerts();
    }

    const queryParams = new URLSearchParams();

    if (filters) {
      if (filters.competitorId) queryParams.append('competitor_id', filters.competitorId);
      if (filters.severity) queryParams.append('severity', filters.severity.join(','));
      if (filters.type) queryParams.append('type', filters.type.join(','));
      if (filters.unreadOnly) queryParams.append('unread_only', 'true');
    }

    const endpoint = `/competitors/alerts${queryParams.toString() ? `?${queryParams}` : ''}`;
    return this.apiRequest<CompetitorAlert[]>(endpoint);
  }

  // Отметить алерт как прочитанный
  async markAlertAsRead(alertId: string): Promise<void> {
    return this.apiRequest<void>(`/competitors/alerts/${alertId}/read`, {
      method: 'POST'
    });
  }

  // Отметить все алерты как прочитанные
  async markAllAlertsAsRead(): Promise<void> {
    return this.apiRequest<void>('/competitors/alerts/read-all', {
      method: 'POST'
    });
  }

  // === АНАЛИТИКА ===

  // Получить аналитику по конкурентам
  async getCompetitorAnalytics(period?: '7d' | '30d' | '90d'): Promise<CompetitorAnalytics> {
    if (USE_MOCK_DATA) {
      return mockCompetitorApi.getCompetitorAnalytics();
    }

    const queryParams = period ? `?period=${period}` : '';
    return this.apiRequest<CompetitorAnalytics>(`/competitors/analytics${queryParams}`);
  }

  // === МОНИТОРИНГ ===

  // Включить/выключить мониторинг конкурента
  async toggleCompetitorMonitoring(competitorId: string, isMonitored: boolean): Promise<void> {
    return this.apiRequest<void>(`/competitors/${competitorId}/monitoring`, {
      method: 'POST',
      body: JSON.stringify({ isMonitored })
    });
  }

  // Запустить ручное обновление данных
  async triggerManualUpdate(competitorId?: string): Promise<void> {
    if (USE_MOCK_DATA) {
      return mockCompetitorApi.triggerManualUpdate();
    }

    const endpoint = competitorId
      ? `/competitors/${competitorId}/update`
      : '/competitors/update-all';

    return this.apiRequest<void>(endpoint, {
      method: 'POST'
    });
  }
}

// Экспортируем единственный экземпляр сервиса
export const competitorApi = new CompetitorApiService();
export default competitorApi;
