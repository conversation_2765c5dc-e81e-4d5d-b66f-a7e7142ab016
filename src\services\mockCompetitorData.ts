// Мок-данные для системы конкурентов
import { 
  Competitor, 
  CompetitorAlert, 
  CompetitorAnalytics,
  CompetitorSearchResult,
  CompetitorProductExtended,
  CompetitorStatus,
  CompetitorType,
  AlertSeverity,
  CompetitorAlertType
} from '../types';

// Мок-данные конкурентов
export const mockCompetitors: Competitor[] = [
  {
    id: '1',
    name: 'TechZone',
    shopId: 'techzone_official',
    rating: 4.8,
    totalProducts: 847,
    averagePriceDifference: 5.2,
    status: CompetitorStatus.ACTIVE,
    lastPriceUpdate: new Date('2024-01-15T10:30:00'),
    competitorType: CompetitorType.MAIN,
    isMonitored: true,
    addedAt: new Date('2024-01-01T00:00:00'),
    updatedAt: new Date('2024-01-15T10:30:00'),
    tags: ['electronics', 'smartphones', 'main_competitor'],
    notes: 'Основной конкурент в категории смартфонов'
  },
  {
    id: '2',
    name: 'MegaTech',
    shopId: 'megatech_store',
    rating: 4.6,
    totalProducts: 1234,
    averagePriceDifference: -2.1,
    status: CompetitorStatus.ACTIVE,
    lastPriceUpdate: new Date('2024-01-15T09:15:00'),
    competitorType: CompetitorType.SECONDARY,
    isMonitored: true,
    addedAt: new Date('2024-01-02T00:00:00'),
    updatedAt: new Date('2024-01-15T09:15:00'),
    tags: ['electronics', 'gadgets']
  },
  {
    id: '3',
    name: 'GadgetPro',
    shopId: 'gadgetpro_shop',
    rating: 2.1,
    totalProducts: 156,
    averagePriceDifference: -15.8,
    status: CompetitorStatus.SUSPICIOUS,
    lastPriceUpdate: new Date('2024-01-15T08:45:00'),
    competitorType: CompetitorType.AGGRESSIVE,
    isMonitored: true,
    addedAt: new Date('2024-01-10T00:00:00'),
    updatedAt: new Date('2024-01-15T08:45:00'),
    tags: ['suspicious', 'dumping'],
    notes: 'Подозрительно низкие цены, возможен демпинг'
  },
  {
    id: '4',
    name: 'ElectroShop',
    shopId: 'electroshop_official',
    rating: 4.4,
    totalProducts: 567,
    averagePriceDifference: 1.2,
    status: CompetitorStatus.ACTIVE,
    lastPriceUpdate: new Date('2024-01-15T07:20:00'),
    competitorType: CompetitorType.SECONDARY,
    isMonitored: true,
    addedAt: new Date('2024-01-05T00:00:00'),
    updatedAt: new Date('2024-01-15T07:20:00'),
    tags: ['electronics', 'accessories']
  },
  {
    id: '5',
    name: 'DigitalWorld',
    shopId: 'digitalworld_store',
    rating: 4.2,
    totalProducts: 389,
    averagePriceDifference: -1.9,
    status: CompetitorStatus.ACTIVE,
    lastPriceUpdate: new Date('2024-01-15T06:10:00'),
    competitorType: CompetitorType.SECONDARY,
    isMonitored: false,
    addedAt: new Date('2024-01-08T00:00:00'),
    updatedAt: new Date('2024-01-15T06:10:00'),
    tags: ['electronics', 'computers']
  }
];

// Мок-данные алертов
export const mockAlerts: CompetitorAlert[] = [
  {
    id: '1',
    type: CompetitorAlertType.PRICE_DROP,
    severity: AlertSeverity.CRITICAL,
    competitorId: '3',
    productId: 'iphone15pro',
    title: 'Критичное снижение цены на iPhone 15 Pro',
    description: 'GadgetPro снизил цену на iPhone 15 Pro на 40% - с 89,990₽ до 53,990₽',
    data: {
      oldPrice: 89990,
      newPrice: 53990,
      difference: -40.0,
      productName: 'iPhone 15 Pro 128GB'
    },
    isRead: false,
    createdAt: new Date('2024-01-15T10:30:00'),
    actionRequired: true,
    suggestedActions: ['Проверить подлинность товара', 'Подать жалобу в Ozon', 'Скорректировать свою цену']
  },
  {
    id: '2',
    type: CompetitorAlertType.SUSPICIOUS_ACTIVITY,
    severity: AlertSeverity.HIGH,
    competitorId: '3',
    title: 'Подозрительные отзывы на AirPods Pro',
    description: '23 идентичных отзыва за 2 часа от новых аккаунтов',
    data: {
      reviewsCount: 23,
      timeframe: '2 hours',
      suspiciousPatterns: ['identical_text', 'new_accounts', 'rapid_posting']
    },
    isRead: false,
    createdAt: new Date('2024-01-15T09:45:00'),
    actionRequired: true,
    suggestedActions: ['Сообщить о нарушении', 'Мониторить активность']
  },
  {
    id: '3',
    type: CompetitorAlertType.OUT_OF_STOCK,
    severity: AlertSeverity.MEDIUM,
    competitorId: '1',
    productId: 'samsung_galaxy_s24',
    title: 'Samsung Galaxy S24 закончился у TechZone',
    description: 'Основной конкурент больше не продает Samsung Galaxy S24',
    data: {
      productName: 'Samsung Galaxy S24 256GB',
      lastAvailablePrice: 79990
    },
    isRead: true,
    createdAt: new Date('2024-01-15T08:20:00'),
    actionRequired: false
  },
  {
    id: '4',
    type: CompetitorAlertType.PRICE_INCREASE,
    severity: AlertSeverity.LOW,
    competitorId: '2',
    title: 'MegaTech поднял цены на наушники',
    description: 'Цены на категорию наушников выросли в среднем на 8%',
    data: {
      category: 'headphones',
      averageIncrease: 8.2,
      affectedProducts: 45
    },
    isRead: true,
    createdAt: new Date('2024-01-15T07:15:00'),
    actionRequired: false
  }
];

// Мок-данные аналитики
export const mockAnalytics: CompetitorAnalytics = {
  totalCompetitors: 5,
  activeCompetitors: 4,
  suspiciousCompetitors: 1,
  averageRating: 4.02,
  averagePriceDifference: -2.48,
  topCompetitors: mockCompetitors.slice(0, 3),
  recentAlerts: mockAlerts.slice(0, 5),
  priceMovements: {
    increases: 12,
    decreases: 28,
    stable: 156
  }
};

// Мок-данные результатов поиска
export const mockSearchResults: CompetitorSearchResult[] = [
  {
    shopId: 'smartdevices_official',
    name: 'SmartDevices',
    rating: 4.6,
    totalProducts: 1234,
    matchingProducts: 78,
    averagePrice: 45000,
    isAlreadyAdded: false,
    suspiciousActivity: false,
    lastActivity: new Date('2024-01-15T10:00:00'),
    categories: ['electronics', 'smartphones', 'accessories']
  },
  {
    shopId: 'electromarket_store',
    name: 'ElectroMarket',
    rating: 4.4,
    totalProducts: 567,
    matchingProducts: 34,
    averagePrice: 42000,
    isAlreadyAdded: false,
    suspiciousActivity: false,
    lastActivity: new Date('2024-01-15T09:30:00'),
    categories: ['electronics', 'gadgets']
  },
  {
    shopId: 'suspiciousshop_fake',
    name: 'SuspiciousShop',
    rating: 2.1,
    totalProducts: 89,
    matchingProducts: 12,
    averagePrice: 25000,
    isAlreadyAdded: false,
    suspiciousActivity: true,
    lastActivity: new Date('2024-01-15T08:00:00'),
    categories: ['electronics']
  }
];

// Мок-данные ИИ-рекомендаций
export const mockAIRecommendations: CompetitorSearchResult[] = [
  {
    shopId: 'technoworld_premium',
    name: 'TechnoWorld',
    rating: 4.7,
    totalProducts: 892,
    matchingProducts: 156,
    averagePrice: 48000,
    isAlreadyAdded: false,
    suspiciousActivity: false,
    lastActivity: new Date('2024-01-15T11:00:00'),
    categories: ['electronics', 'premium_gadgets']
  },
  {
    shopId: 'digitech_solutions',
    name: 'DigiTech Solutions',
    rating: 4.5,
    totalProducts: 445,
    matchingProducts: 89,
    averagePrice: 43000,
    isAlreadyAdded: false,
    suspiciousActivity: false,
    lastActivity: new Date('2024-01-15T10:45:00'),
    categories: ['electronics', 'business_solutions']
  }
];

// Функция для симуляции задержки API
export const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Мок API функции
export const mockCompetitorApi = {
  async getCompetitors() {
    await delay(800);
    return mockCompetitors;
  },

  async getCompetitorAlerts() {
    await delay(600);
    return mockAlerts;
  },

  async getCompetitorAnalytics() {
    await delay(1000);
    return mockAnalytics;
  },

  async searchCompetitorsByProduct(query: string) {
    await delay(1200);
    return mockSearchResults.filter(result => 
      result.name.toLowerCase().includes(query.toLowerCase()) ||
      result.categories?.some(cat => cat.toLowerCase().includes(query.toLowerCase()))
    );
  },

  async searchCompetitorsByShop(query: string) {
    await delay(1000);
    return mockSearchResults.filter(result => 
      result.name.toLowerCase().includes(query.toLowerCase())
    );
  },

  async getAICompetitorRecommendations() {
    await delay(1500);
    return mockAIRecommendations;
  },

  async addCompetitor(shopId: string, competitorType: CompetitorType) {
    await delay(800);
    const newCompetitor: Competitor = {
      id: Date.now().toString(),
      name: `New Competitor ${shopId}`,
      shopId,
      rating: 4.0,
      totalProducts: 100,
      averagePriceDifference: 0,
      status: CompetitorStatus.ACTIVE,
      lastPriceUpdate: new Date(),
      competitorType,
      isMonitored: true,
      addedAt: new Date(),
      updatedAt: new Date()
    };
    mockCompetitors.push(newCompetitor);
    return newCompetitor;
  },

  async triggerManualUpdate() {
    await delay(2000);
    // Симуляция обновления данных
    return;
  }
};
