import { extendTheme, type ThemeConfig } from '@chakra-ui/react';

// Конфигурация темы
const config: ThemeConfig = {
  initialColorMode: 'light',
  useSystemColorMode: true,
};

// Современная цветовая палитра
const colors = {
  // Основная палитра - современный синий с градиентами
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6', // Основной цвет - современный синий
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
    950: '#172554',
  },

  // Акцентная палитра - фиолетовый для премиум функций
  accent: {
    50: '#faf5ff',
    100: '#f3e8ff',
    200: '#e9d5ff',
    300: '#d8b4fe',
    400: '#c084fc',
    500: '#a855f7',
    600: '#9333ea',
    700: '#7c3aed',
    800: '#6b21a8',
    900: '#581c87',
    950: '#3b0764',
  },

  // Успех - современный зеленый
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
    950: '#052e16',
  },

  // Предупреждение - янтарный
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
    950: '#451a03',
  },

  // Ошибка - красный
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
    950: '#450a0a',
  },

  // Нейтральные цвета - современная серая палитра
  gray: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a',
    950: '#020617',
  },

  // Специальные цвета для бизнес-логики
  ozon: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#0052cc', // Фирменный цвет Ozon
    600: '#0041a3',
    700: '#00337a',
    800: '#002651',
    900: '#001a33',
  },

  // Цвета для статусов товаров
  status: {
    active: '#22c55e',
    inactive: '#64748b',
    pending: '#f59e0b',
    rejected: '#ef4444',
    archived: '#8b5cf6',
  },
};

// Современные стили компонентов
const components = {
  Button: {
    baseStyle: {
      fontWeight: '600',
      borderRadius: 'lg',
      transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
      _focus: {
        boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.1)',
      },
    },
    sizes: {
      sm: {
        h: '8',
        minW: '8',
        fontSize: 'sm',
        px: '3',
      },
      md: {
        h: '10',
        minW: '10',
        fontSize: 'md',
        px: '4',
      },
      lg: {
        h: '12',
        minW: '12',
        fontSize: 'lg',
        px: '6',
      },
      xl: {
        h: '14',
        minW: '14',
        fontSize: 'xl',
        px: '8',
      },
    },
    variants: {
      solid: (props: any) => ({
        bg: 'primary.500',
        color: 'white',
        _hover: {
          bg: 'primary.600',
          transform: 'translateY(-1px)',
          boxShadow: 'lg',
        },
        _active: {
          bg: 'primary.700',
          transform: 'translateY(0)',
        },
      }),
      outline: (props: any) => ({
        borderColor: 'primary.500',
        color: 'primary.500',
        borderWidth: '2px',
        _hover: {
          bg: 'primary.50',
          borderColor: 'primary.600',
          transform: 'translateY(-1px)',
        },
        _active: {
          bg: 'primary.100',
          transform: 'translateY(0)',
        },
      }),
      ghost: (props: any) => ({
        color: 'primary.500',
        _hover: {
          bg: 'primary.50',
          transform: 'translateY(-1px)',
        },
        _active: {
          bg: 'primary.100',
          transform: 'translateY(0)',
        },
      }),
      gradient: {
        bgGradient: 'linear(to-r, primary.500, accent.500)',
        color: 'white',
        _hover: {
          bgGradient: 'linear(to-r, primary.600, accent.600)',
          transform: 'translateY(-1px)',
          boxShadow: 'lg',
        },
        _active: {
          bgGradient: 'linear(to-r, primary.700, accent.700)',
          transform: 'translateY(0)',
        },
      },
    },
  },

  Card: {
    baseStyle: (props: any) => ({
      container: {
        bg: props.colorMode === 'dark' ? 'gray.800' : 'white',
        borderRadius: 'xl',
        boxShadow: props.colorMode === 'dark'
          ? '0 4px 6px -1px rgba(0, 0, 0, 0.3)'
          : '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        border: '1px solid',
        borderColor: props.colorMode === 'dark' ? 'gray.700' : 'gray.200',
        transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
        _hover: {
          transform: 'translateY(-2px)',
          boxShadow: props.colorMode === 'dark'
            ? '0 10px 15px -3px rgba(0, 0, 0, 0.4)'
            : '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
        },
      },
    }),
  },

  Heading: {
    baseStyle: {
      fontWeight: '700',
      letterSpacing: '-0.025em',
    },
    sizes: {
      xs: {
        fontSize: 'sm',
        lineHeight: '1.25',
      },
      sm: {
        fontSize: 'md',
        lineHeight: '1.375',
      },
      md: {
        fontSize: 'lg',
        lineHeight: '1.5',
      },
      lg: {
        fontSize: 'xl',
        lineHeight: '1.5',
      },
      xl: {
        fontSize: '2xl',
        lineHeight: '1.33',
      },
      '2xl': {
        fontSize: '3xl',
        lineHeight: '1.2',
      },
    },
  },

  Input: {
    variants: {
      outline: (props: any) => ({
        field: {
          borderColor: props.colorMode === 'dark' ? 'gray.600' : 'gray.300',
          borderRadius: 'lg',
          _hover: {
            borderColor: props.colorMode === 'dark' ? 'gray.500' : 'gray.400',
          },
          _focus: {
            borderColor: 'primary.500',
            boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.1)',
          },
        },
      }),
    },
  },

  Badge: {
    baseStyle: {
      fontWeight: '600',
      borderRadius: 'full',
      textTransform: 'none',
      fontSize: 'xs',
    },
    variants: {
      solid: (props: any) => {
        const { colorScheme } = props;
        return {
          bg: `${colorScheme}.500`,
          color: 'white',
        };
      },
      subtle: (props: any) => {
        const { colorScheme } = props;
        return {
          bg: `${colorScheme}.100`,
          color: `${colorScheme}.800`,
        };
      },
    },
  },
};

// Современные глобальные стили
const styles = {
  global: (props: any) => ({
    body: {
      bg: props.colorMode === 'dark' ? 'gray.900' : 'gray.50',
      color: props.colorMode === 'dark' ? 'gray.100' : 'gray.900',
      fontFeatureSettings: '"cv02", "cv03", "cv04", "cv11"',
      fontVariantNumeric: 'oldstyle-nums',
      lineHeight: '1.6',
    },
    '*': {
      borderColor: props.colorMode === 'dark' ? 'gray.700' : 'gray.200',
    },
    '*::placeholder': {
      color: props.colorMode === 'dark' ? 'gray.400' : 'gray.500',
    },
    '*, *::before, &::after': {
      borderColor: props.colorMode === 'dark' ? 'gray.700' : 'gray.200',
    },
    // Улучшенная прокрутка
    '::-webkit-scrollbar': {
      width: '8px',
      height: '8px',
    },
    '::-webkit-scrollbar-track': {
      bg: props.colorMode === 'dark' ? 'gray.800' : 'gray.100',
      borderRadius: 'full',
    },
    '::-webkit-scrollbar-thumb': {
      bg: props.colorMode === 'dark' ? 'gray.600' : 'gray.300',
      borderRadius: 'full',
      _hover: {
        bg: props.colorMode === 'dark' ? 'gray.500' : 'gray.400',
      },
    },
  }),
};

// Современная типографическая система
const fonts = {
  heading: `'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif`,
  body: `'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif`,
  mono: `'JetBrains Mono', 'Fira Code', 'Cascadia Code', Consolas, 'Courier New', monospace`,
};

// Система отступов и размеров
const space = {
  px: '1px',
  0.5: '0.125rem',
  1: '0.25rem',
  1.5: '0.375rem',
  2: '0.5rem',
  2.5: '0.625rem',
  3: '0.75rem',
  3.5: '0.875rem',
  4: '1rem',
  5: '1.25rem',
  6: '1.5rem',
  7: '1.75rem',
  8: '2rem',
  9: '2.25rem',
  10: '2.5rem',
  12: '3rem',
  14: '3.5rem',
  16: '4rem',
  20: '5rem',
  24: '6rem',
  28: '7rem',
  32: '8rem',
  36: '9rem',
  40: '10rem',
  44: '11rem',
  48: '12rem',
  52: '13rem',
  56: '14rem',
  60: '15rem',
  64: '16rem',
  72: '18rem',
  80: '20rem',
  96: '24rem',
};

// Система теней
const shadows = {
  xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  sm: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
  none: 'none',
  // Специальные тени для карточек
  card: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  'card-hover': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  'card-focus': '0 0 0 3px rgba(59, 130, 246, 0.1)',
};

// Создаем расширенную тему
const theme = extendTheme({
  config,
  colors,
  components,
  styles,
  fonts,
  space,
  shadows,
  // Брейкпоинты для адаптивности
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },
  // Радиусы скругления
  radii: {
    none: '0',
    sm: '0.125rem',
    base: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    '2xl': '1rem',
    '3xl': '1.5rem',
    full: '9999px',
  },
  // Переходы и анимации
  transition: {
    property: {
      common: 'background-color, border-color, color, fill, stroke, opacity, box-shadow, transform',
      colors: 'background-color, border-color, color, fill, stroke',
      dimensions: 'width, height',
      position: 'left, right, top, bottom',
      background: 'background-color, background-image, background-position',
    },
    easing: {
      'ease-in': 'cubic-bezier(0.4, 0, 1, 1)',
      'ease-out': 'cubic-bezier(0, 0, 0.2, 1)',
      'ease-in-out': 'cubic-bezier(0.4, 0, 0.2, 1)',
    },
    duration: {
      'ultra-fast': '50ms',
      faster: '100ms',
      fast: '150ms',
      normal: '200ms',
      slow: '300ms',
      slower: '500ms',
      'ultra-slow': '1000ms',
    },
  },
});

export default theme;
